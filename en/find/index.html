<!DOCTYPE html><html lang="en"><head><meta charset="utf-8" /><meta property="og:site_name" content="" /><meta property="og:type" content="article" /><meta property="og:url" content="/find-en" /><meta content="summary_large_image" name="twitter:card" />          <meta property="og:title" content="Find a Music Therapist" />     <meta name="twitter:title" content="Find a Music Therapist" />      <meta property="og:description" content="We can help connect you with specialized professionals" />     <meta name="twitter:description" content="We can help connect you with specialized professionals" />      <meta property="og:image" content="../../assets/527bd81a8f884dfea6ac825419b07348.webp" />     <meta property="twitter:image" content="../../assets/527bd81a8f884dfea6ac825419b07348.webp">      <title>Find a Music Therapist</title>      <meta name="description" content="We can help connect you with specialized professionals" />            <link rel="canonical" href="https://www.cymta.org/en/find" /><link rel="alternate" hreflang="el" href="https://www.cymta.org/find" /><link rel="alternate" hreflang="en" href="https://www.cymta.org/en/find" /><link rel="icon" href="https://cloud-1de12d.b-cdn.net/media/iW=32%26iH=any/5800e034b05aa57cedaa6e50ab5154b2.png" sizes="32x32"/><link rel="icon" href="https://cloud-1de12d.b-cdn.net/media/iW=192%26iH=any/5800e034b05aa57cedaa6e50ab5154b2.png" sizes="192x192"/><link rel="apple-touch-icon-precomposed" href="https://cloud-1de12d.b-cdn.net/media/iW=180&iH=any/5800e034b05aa57cedaa6e50ab5154b2.png"/><meta name="viewport" content="width=device-width, initial-scale=1"><link class="brz-link brz-link-bunny-fonts-prefetch" rel="dns-prefetch" href="//fonts.bunny.net"><link class="brz-link brz-link-bunny-fonts-preconnect" rel="preconnect" href="https://fonts.bunny.net/" crossorigin><link class="brz-link brz-link-cdn-preconnect" rel="preconnect" href="https://cloud-1de12d.b-cdn.net" crossorigin><link href="https://fonts.bunny.net/css?family=Comfortaa:300,regular,500,600,700|Lato:100,100italic,300,300italic,regular,italic,700,700italic,900,900italic&subset=arabic,bengali,cyrillic,cyrillic-ext,devanagari,greek,greek-ext,gujarati,hebrew,khmer,korean,latin-ext,tamil,telugu,thai,vietnamese&display=swap" class="brz-link brz-link-google" type="text/css" rel="stylesheet"/><link href="../../assets/eb37fe33c69c8176a947e723f64dd277.css" class="brz-link brz-link-preview-lib" data-brz-group="group-1_3" rel="stylesheet"/><link href="../../assets/07575d7e3674e1a8b8d8a30025ca06cb.css" class="brz-link brz-link-preview-lib-pro" data-brz-group="group-1_2" rel="stylesheet"/><link href="../../assets/a1c351b066e704f38f6ab6530f000598.css" class="brz-link brz-link-preview-pro" rel="stylesheet"/><style class="brz-style">.brz .brz-css-310oex {z-index: auto;margin: 0;}
.brz .brz-css-310oex.brz-section .brz-section__content {min-height: auto;display: flex;}
.brz .brz-css-310oex .brz-container {justify-content: center;}
.brz .brz-css-310oex > .slick-slider > .brz-slick-slider__dots {color: rgba(0,0,0,1);}
.brz .brz-css-310oex > .slick-slider > .brz-slick-slider__arrow {color: rgba(0,0,0,.7);}
@media (min-width:991px) {.brz .brz-css-310oex {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-310oex {display: block;}}
@media (max-width:767px) {.brz .brz-css-310oex {display: block;}}
.brz .brz-css-1v4fvfm {margin: -100px 0px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1v4fvfm {z-index: auto;margin: -100px 0px 0px 0px;}
	.brz .brz-css-1v4fvfm.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-1v4fvfm .brz-container {justify-content: center;}
	.brz .brz-css-1v4fvfm > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-1v4fvfm > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-1v4fvfm:hover {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1v4fvfm {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-1v4fvfm {margin: 0;}}
.brz .brz-css-kt508o {padding: 75px 0px 75px 0px;}
.brz .brz-css-kt508o > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-kt508o > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
.brz .brz-css-kt508o > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}
@media (min-width:991px) {.brz .brz-css-kt508o > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-kt508o {padding: 50px 15px 50px 15px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-kt508o > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-kt508o {padding: 25px 15px 25px 15px;}}
@media (max-width:767px) {.brz .brz-css-kt508o > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-kt508o > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-faw0zd {padding: 290px 48px 206px 48px;}
.brz .brz-css-faw0zd > .brz-bg > .brz-bg-image {background-image: url("../../assets/ab203be0c809de903cd06a6989576d30.webp");background-position: 76% 100%;}
.brz .brz-css-faw0zd > .brz-bg > .brz-bg-image:after {content: "";background-image: url("../../assets/ab203be0c809de903cd06a6989576d30.webp");}
.brz .brz-css-faw0zd > .brz-bg > .brz-bg-color {background-color: transparent;background-image: linear-gradient(25deg,rgba(39,129,126,.87) 0%,rgba(var(--brz-global-color8),.66) 100%);}
@media (min-width:991px) {.brz .brz-css-faw0zd > .brz-bg > .brz-bg-image {background-attachment: fixed;}}
@media (min-width:991px) {.brz .brz-css-faw0zd {padding: 290px 48px 206px 48px;}
	.brz .brz-css-faw0zd > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-faw0zd:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-image {background-image: url("../../assets/ab203be0c809de903cd06a6989576d30.webp");filter: none;background-position: 76% 100%;display: block;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: url("../../assets/ab203be0c809de903cd06a6989576d30.webp");}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-color {background-color: transparent;background-image: linear-gradient(25deg,rgba(39,129,126,.87) 0%,rgba(var(--brz-global-color8),.66) 100%);}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-faw0zd:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-image {background-attachment: fixed;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-faw0zd:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-faw0zd {padding: 128px 24px 128px 24px;}}
@media (max-width:767px) {.brz .brz-css-faw0zd {padding: 108px 20px 108px 20px;}
	.brz .brz-css-faw0zd > .brz-bg > .brz-bg-image {background-position: 75% 0%;}}
.brz .brz-css-w4i6p2 {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-w4i6p2 {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-w4i6p2 {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-w4i6p2 {max-width: 100%;}}
@media (min-width:991px) {.brz .brz-css-1lbvlkv {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (min-width:991px) {.brz .brz-css-1lbvlkv:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1lbvlkv {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1lbvlkv {max-width: 75%;}}
@media (max-width:767px) {.brz .brz-css-1lbvlkv {max-width: 100%;}}
.brz .brz-css-3qbalv {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-3qbalv .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-3qbalv {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-3qbalv {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-3qbalv {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-5dgfw4 {padding: 0px 10% 0px 10%;margin: 0;}
@media (min-width:991px) {.brz .brz-css-5dgfw4 {padding: 0px 10% 0px 10%;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-5dgfw4 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-5dgfw4 {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-5dgfw4 {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-5dgfw4 {padding: 0;}}
.brz .brz-css-13d31ya {animation-name: none;animation-duration: 1000ms;animation-delay: 1000ms;animation-iteration-count: unset;}
.brz .brz-css-1csnxia {animation-name: fadeInLeft;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}
@media (min-width:991px) {.brz .brz-css-1csnxia {animation-name: fadeInLeft;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}}
.brz .brz-css-1veoybo {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-1d9stus {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-bKPsZ {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading1fontfamily,initial) !important;font-size: var(--brz-heading1fontsize,initial);line-height: var(--brz-heading1lineheight,initial);font-weight: var(--brz-heading1fontweight,initial);font-weight: var(--brz-heading1bold,initial);letter-spacing: var(--brz-heading1letterspacing,initial);font-variation-settings: var(--brz-heading1fontvariation,initial);font-style: var(--brz-heading1italic,initial);text-decoration: var(--brz-heading1textdecoration,initial) !important;text-transform: var(--brz-heading1texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-bKPsZ {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading1fontfamily,initial) !important;font-size: var(--brz-heading1fontsize,initial);line-height: var(--brz-heading1lineheight,initial);font-weight: var(--brz-heading1fontweight,initial);font-weight: var(--brz-heading1bold,initial);letter-spacing: var(--brz-heading1letterspacing,initial);font-variation-settings: var(--brz-heading1fontvariation,initial);font-style: var(--brz-heading1italic,initial);text-decoration: var(--brz-heading1textdecoration,initial) !important;text-transform: var(--brz-heading1texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bKPsZ {font-size: var(--brz-heading1tabletfontsize,initial);line-height: var(--brz-heading1tabletlineheight,initial);font-weight: var(--brz-heading1tabletfontweight,initial);font-weight: var(--brz-heading1tabletbold,initial);letter-spacing: var(--brz-heading1tabletletterspacing,initial);font-variation-settings: var(--brz-heading1tabletfontvariation,initial);font-style: var(--brz-heading1tabletitalic,initial);text-decoration: var(--brz-heading1tablettextdecoration,initial) !important;text-transform: var(--brz-heading1tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-bKPsZ {text-align: center !important;font-size: var(--brz-heading1mobilefontsize,initial);line-height: var(--brz-heading1mobilelineheight,initial);font-weight: var(--brz-heading1mobilefontweight,initial);font-weight: var(--brz-heading1mobilebold,initial);letter-spacing: var(--brz-heading1mobileletterspacing,initial);font-variation-settings: var(--brz-heading1mobilefontvariation,initial);font-style: var(--brz-heading1mobileitalic,initial);text-decoration: var(--brz-heading1mobiletextdecoration,initial) !important;text-transform: var(--brz-heading1mobiletexttransform,initial) !important;}}
.brz .brz-css-15r3e4t {padding: 0px 50% 0px 10%;margin: 10px 0px 10px 0px;}
@media (min-width:991px) {.brz .brz-css-15r3e4t {padding: 0px 50% 0px 10%;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
	.brz .brz-css-15r3e4t .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-15r3e4t {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-15r3e4t {padding: 0px 35% 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-15r3e4t {padding: 0;margin: 8px 0px 0px 0px;}}
.brz .brz-css-hwinur {animation-name: none;animation-duration: 1000ms;animation-delay: 1000ms;animation-iteration-count: unset;}
.brz .brz-css-cw7fqk {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}
@media (min-width:991px) {.brz .brz-css-cw7fqk {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}}
@media (min-width:991px) {.brz .brz-css-gi2ck9 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-eh_jO {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-eh_jO {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-eh_jO {font-size: var(--brz-subtitletabletfontsize,initial);line-height: var(--brz-subtitletabletlineheight,initial);font-weight: var(--brz-subtitletabletfontweight,initial);font-weight: var(--brz-subtitletabletbold,initial);letter-spacing: var(--brz-subtitletabletletterspacing,initial);font-variation-settings: var(--brz-subtitletabletfontvariation,initial);font-style: var(--brz-subtitletabletitalic,initial);text-decoration: var(--brz-subtitletablettextdecoration,initial) !important;text-transform: var(--brz-subtitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-eh_jO {text-align: center !important;font-size: var(--brz-subtitlemobilefontsize,initial);line-height: var(--brz-subtitlemobilelineheight,initial);font-weight: var(--brz-subtitlemobilefontweight,initial);font-weight: var(--brz-subtitlemobilebold,initial);letter-spacing: var(--brz-subtitlemobileletterspacing,initial);font-variation-settings: var(--brz-subtitlemobilefontvariation,initial);font-style: var(--brz-subtitlemobileitalic,initial);text-decoration: var(--brz-subtitlemobiletextdecoration,initial) !important;text-transform: var(--brz-subtitlemobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1oswvkr {z-index: auto;margin: 0;}
	.brz .brz-css-1oswvkr.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-1oswvkr .brz-container {justify-content: center;}
	.brz .brz-css-1oswvkr > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-1oswvkr > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-1oswvkr:hover {display: block;}}
.brz .brz-css-1t4bujf {padding: 128px 48px 120px 48px;}
.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-color {background-color: rgba(240,240,247,0);}
@media (min-width:991px) {.brz .brz-css-1t4bujf {padding: 128px 48px 120px 48px;}
	.brz .brz-css-1t4bujf > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-1t4bujf:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-color {background-color: rgba(240,240,247,0);background-image: none;}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1t4bujf > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1t4bujf:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t4bujf:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1t4bujf {padding: 80px 44px 72px 44px;}}
@media (max-width:767px) {.brz .brz-css-1t4bujf {padding: 60px 30px 44px 30px;}}
@media (min-width:991px) {.brz .brz-css-1llvq5f:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1llvq5f {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-1atdaet {padding: 0px 10% 0px 10%;margin: 0px 0px 24px 0px;}
@media (min-width:991px) {.brz .brz-css-1atdaet {padding: 0px 10% 0px 10%;margin: 0px 0px 24px 0px;justify-content: center;position: relative;}
	.brz .brz-css-1atdaet .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1atdaet {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1atdaet {padding: 0;margin: 0px 0px 16px 0px;}}
@media (max-width:767px) {.brz .brz-css-1atdaet {padding: 0;margin: 0;}}
@media (min-width:991px) {.brz .brz-css-1rb367g {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-tn7NM {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading2fontfamily,initial) !important;font-size: var(--brz-heading2fontsize,initial);line-height: var(--brz-heading2lineheight,initial);font-weight: var(--brz-heading2fontweight,initial);font-weight: var(--brz-heading2bold,initial);letter-spacing: var(--brz-heading2letterspacing,initial);font-variation-settings: var(--brz-heading2fontvariation,initial);font-style: var(--brz-heading2italic,initial);text-decoration: var(--brz-heading2textdecoration,initial) !important;text-transform: var(--brz-heading2texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-tn7NM {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading2fontfamily,initial) !important;font-size: var(--brz-heading2fontsize,initial);line-height: var(--brz-heading2lineheight,initial);font-weight: var(--brz-heading2fontweight,initial);font-weight: var(--brz-heading2bold,initial);letter-spacing: var(--brz-heading2letterspacing,initial);font-variation-settings: var(--brz-heading2fontvariation,initial);font-style: var(--brz-heading2italic,initial);text-decoration: var(--brz-heading2textdecoration,initial) !important;text-transform: var(--brz-heading2texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-tn7NM {font-size: var(--brz-heading2tabletfontsize,initial);line-height: var(--brz-heading2tabletlineheight,initial);font-weight: var(--brz-heading2tabletfontweight,initial);font-weight: var(--brz-heading2tabletbold,initial);letter-spacing: var(--brz-heading2tabletletterspacing,initial);font-variation-settings: var(--brz-heading2tabletfontvariation,initial);font-style: var(--brz-heading2tabletitalic,initial);text-decoration: var(--brz-heading2tablettextdecoration,initial) !important;text-transform: var(--brz-heading2tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-tn7NM {text-align: center !important;font-size: var(--brz-heading2mobilefontsize,initial);line-height: var(--brz-heading2mobilelineheight,initial);font-weight: var(--brz-heading2mobilefontweight,initial);font-weight: var(--brz-heading2mobilebold,initial);letter-spacing: var(--brz-heading2mobileletterspacing,initial);font-variation-settings: var(--brz-heading2mobilefontvariation,initial);font-style: var(--brz-heading2mobileitalic,initial);text-decoration: var(--brz-heading2mobiletextdecoration,initial) !important;text-transform: var(--brz-heading2mobiletexttransform,initial) !important;}}
.brz .brz-css-1y06q6o {padding: 0px 10% 0px 10%;margin: 0;}
@media (min-width:991px) {.brz .brz-css-1y06q6o {padding: 0px 10% 0px 10%;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1y06q6o .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1y06q6o {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1y06q6o {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1y06q6o {padding: 0;margin: 24px 0px 0px 0px;}}
@media (min-width:991px) {.brz .brz-css-1h0e4l7 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-bCdaM {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-bCdaM {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bCdaM {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-bCdaM {text-align: center !important;font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-wGEfh {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-wGEfh {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-wGEfh {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-wGEfh {text-align: center !important;font-size: 18px;line-height: 1.5;font-weight: 100;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
.brz .brz-css-sTTUw {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-sTTUw {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sTTUw {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-sTTUw {text-align: center !important;font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-1abvqnm {z-index: auto;position: relative;margin: 10px 0px 10px 0px;justify-content: center;padding: 0;gap: 20px 10px;}
@media (min-width:991px) {.brz .brz-css-1abvqnm {position: relative;}
	.brz .brz-css-1abvqnm {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1abvqnm {position: relative;}
	.brz .brz-css-1abvqnm {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1abvqnm {position: relative;}
	.brz .brz-css-1abvqnm {display: flex;}}
.brz .brz-css-h4enic {margin: 24px 0px 24px 0px;justify-content: flex-start;padding: 0px 10% 0px 10%;}
@media (min-width:991px) {.brz .brz-css-h4enic {z-index: auto;position: relative;margin: 24px 0px 24px 0px;justify-content: flex-start;padding: 0px 10% 0px 10%;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-h4enic {position: relative;}
	.brz .brz-css-h4enic:hover {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-h4enic {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-h4enic {margin: 0;justify-content: center;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-h4enic {display: none;}}
.brz .brz-css-ux52jh {justify-content: center;padding: 0;gap: 20px 10px;}
.brz .brz-css-13p817q {justify-content: flex-start;padding: 0px 10% 0px 10%;}
@media (min-width:991px) {.brz .brz-css-13p817q {justify-content: flex-start;padding: 0px 10% 0px 10%;gap: 20px 10px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-13p817q {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-13p817q {justify-content: center;padding: 0;}}
.brz .brz-css-1dzh008.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1dzh008.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1dzh008.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1dzh008.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
@media (min-width:991px) {.brz .brz-css-1dzh008.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1dzh008.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1dzh008.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1dzh008.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1dzh008.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1dzh008.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1dzh008.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1dzh008.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1dzh008.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1dzh008.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:767px) {.brz .brz-css-1dzh008.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1dzh008.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1dzh008.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1dzh008.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1dzh008.brz-back-pulse:before {animation-duration: .6s;}}
.brz .brz-css-1emvac7.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1emvac7.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1emvac7.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1emvac7.brz-btn--hover-in {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1emvac7.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1emvac7.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-1emvac7.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1emvac7.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1emvac7.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1emvac7.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1emvac7.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1emvac7.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1emvac7.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1d74kyl.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 0;padding: 14px 42px 14px 42px;padding: 14px 42px;flex-flow: row-reverse nowrap;}
.brz .brz-css-1d74kyl.brz-btn {display: flex;color: rgba(var(--brz-global-color8),1);border: 2px solid rgba(var(--brz-global-color3),1);box-shadow: none;}
.brz .brz-css-1d74kyl.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1d74kyl.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-1d74kyl.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1d74kyl:after {height: unset;}
.brz .brz-css-1d74kyl .brz-btn--story-container {border: 2px solid rgba(var(--brz-global-color3),1);flex-flow: row-reverse nowrap;border-radius: 0;}
.brz .brz-css-1d74kyl .brz-btn--story-container:after {height: unset;}
@media (min-width:991px) {.brz .brz-css-1d74kyl.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (min-width:991px) {.brz .brz-css-1d74kyl.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-1d74kyl.brz-btn.brz-btn-submit:hover {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1d74kyl.brz-btn {font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);font-size: var(--brz-buttontabletfontsize,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1d74kyl.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-1d74kyl.brz-btn {font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);font-size: var(--brz-buttonmobilefontsize,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:767px) {.brz .brz-css-1d74kyl.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1d74kyl .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-l5n4cz.brz-btn {border-radius: 16px;padding: 17px 45px 17px 45px;padding: 17px 45px;}
.brz .brz-css-l5n4cz.brz-btn {border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-l5n4cz.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-l5n4cz.brz-btn.brz-btn-submit {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-l5n4cz .brz-btn--story-container {border: 0px solid rgba(35,157,219,0);border-radius: 16px;}
@media (min-width:991px) {.brz .brz-css-l5n4cz.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 16px;padding: 17px 45px 17px 45px;padding: 17px 45px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-l5n4cz.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(84,53,240,.8);box-shadow: none;}
	.brz .brz-css-l5n4cz.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
	.brz .brz-css-l5n4cz.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-l5n4cz.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color3),1);background-image: none;}
	.brz .brz-css-l5n4cz:after {height: unset;}
	.brz .brz-css-l5n4cz .brz-btn--story-container {border: 0px solid rgba(84,53,240,.8);flex-flow: row-reverse nowrap;border-radius: 16px;}
	.brz .brz-css-l5n4cz .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-l5n4cz.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-l5n4cz.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-l5n4cz.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-l5n4cz .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-l5n4cz.brz-btn {border-radius: 0px;padding: 14px 42px 14px 42px;padding: 14px 42px;}
	.brz .brz-css-l5n4cz .brz-btn--story-container {border-radius: 0px;}}
@media (max-width:767px) {.brz .brz-css-l5n4cz.brz-btn {border-radius: 0px;padding: 14px 42px 14px 42px;padding: 14px 42px;}
	.brz .brz-css-l5n4cz .brz-btn--story-container {border-radius: 0px;}}
.brz .brz-css-1wiyjm0 {margin: 0;justify-content: flex-start;padding: 0px 10% 0px 10%;}
@media (min-width:991px) {.brz .brz-css-1wiyjm0 {display: none;}}
@media (min-width:991px) {.brz .brz-css-1wiyjm0 {z-index: auto;position: relative;margin: 0;justify-content: flex-start;padding: 0px 10% 0px 10%;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1wiyjm0 {position: relative;}
	.brz .brz-css-1wiyjm0:hover {display: flex;display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1wiyjm0 {padding: 0;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1wiyjm0 {display: none;}}
@media (max-width:767px) {.brz .brz-css-1wiyjm0 {margin: 24px 0px 0px 0px;justify-content: center;padding: 0;}}
.brz .brz-css-wdcd2o {justify-content: flex-start;padding: 0px 10% 0px 10%;}
@media (min-width:991px) {.brz .brz-css-wdcd2o {justify-content: flex-start;padding: 0px 10% 0px 10%;gap: 20px 10px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-wdcd2o {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-wdcd2o {justify-content: center;padding: 0;}}
.brz .brz-css-1qgxz9q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1qgxz9q.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1qgxz9q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1qgxz9q.brz-btn--hover-in {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1qgxz9q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1qgxz9q.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-1qgxz9q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1qgxz9q.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1qgxz9q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1qgxz9q.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1qgxz9q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1qgxz9q.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1qgxz9q.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1t73cfz.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 16px;padding: 17px 45px 17px 45px;padding: 17px 45px;}
.brz .brz-css-1t73cfz.brz-btn {border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1t73cfz.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1t73cfz.brz-btn.brz-btn-submit {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1t73cfz .brz-btn--story-container {border: 0px solid rgba(35,157,219,0);border-radius: 16px;}
@media (min-width:991px) {.brz .brz-css-1t73cfz.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 16px;padding: 17px 45px 17px 45px;padding: 17px 45px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1t73cfz.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(84,53,240,.8);box-shadow: none;}
	.brz .brz-css-1t73cfz.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
	.brz .brz-css-1t73cfz.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1t73cfz.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color3),1);background-image: none;}
	.brz .brz-css-1t73cfz:after {height: unset;}
	.brz .brz-css-1t73cfz .brz-btn--story-container {border: 0px solid rgba(84,53,240,.8);flex-flow: row-reverse nowrap;border-radius: 16px;}
	.brz .brz-css-1t73cfz .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1t73cfz.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t73cfz.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t73cfz.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t73cfz .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1t73cfz.brz-btn {font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);font-size: var(--brz-buttontabletfontsize,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;border-radius: 0px;padding: 14px 42px 14px 42px;padding: 14px 42px;}
	.brz .brz-css-1t73cfz .brz-btn--story-container {border-radius: 0px;}}
@media (max-width:767px) {.brz .brz-css-1t73cfz.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;border-radius: 0px;padding: 14px 42px 14px 42px;padding: 14px 42px;}
	.brz .brz-css-1t73cfz .brz-btn--story-container {border-radius: 0px;}}
.brz .brz-css-jah8c5 {padding: 0px 10% 0px 10%;margin: 0px 0px 24px 0px;}
@media (min-width:991px) {.brz .brz-css-jah8c5 {padding: 0px 10% 0px 10%;margin: 0px 0px 24px 0px;justify-content: center;position: relative;}
	.brz .brz-css-jah8c5 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-jah8c5 {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-jah8c5 {padding: 0;margin: 0;}}
@media (max-width:767px) {.brz .brz-css-jah8c5 {padding: 0;margin: 24px 0px 0px 0px;}}
@media (min-width:991px) {.brz .brz-css-dq39l5 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-nr6C0 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-nr6C0 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-nr6C0 {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-nr6C0 {text-align: center !important;font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-4ky6vy {margin: 0;}
@media (min-width:991px) {.brz .brz-css-yxekvk {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-yxekvk .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-yxekvk {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-yxekvk {display: none;}}
@media (max-width:767px) {.brz .brz-css-yxekvk {display: none;}}
.brz .brz-css-1a6zrza {height: 50px;}
.brz .brz-css-9dks9p {height: 47px;}
@media (min-width:991px) {.brz .brz-css-9dks9p {height: 47px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-9dks9p {height: 35px;}}
@media (max-width:767px) {.brz .brz-css-9dks9p {height: 25px;}}
.brz .brz-css-bll4jo {z-index: auto;margin: 0;}
.brz .brz-css-bll4jo.brz-section .brz-section__content {min-height: auto;display: flex;}
.brz .brz-css-bll4jo .brz-container {justify-content: center;}
.brz .brz-css-bll4jo > .slick-slider > .brz-slick-slider__dots {color: rgba(0,0,0,1);}
.brz .brz-css-bll4jo > .slick-slider > .brz-slick-slider__arrow {color: rgba(0,0,0,.7);}
@media (min-width:991px) {.brz .brz-css-bll4jo {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bll4jo {display: block;}}
@media (max-width:767px) {.brz .brz-css-bll4jo {display: block;}}
.brz .brz-css-1ijukrt {margin: 0px 48px 0px 48px;}
@media (min-width:991px) {.brz .brz-css-1ijukrt {z-index: auto;margin: 0px 48px 0px 48px;}
	.brz .brz-css-1ijukrt.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-1ijukrt .brz-container {justify-content: center;}
	.brz .brz-css-1ijukrt > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-1ijukrt > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-1ijukrt:hover {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1ijukrt {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-1ijukrt {margin: 0;}}
.brz .brz-css-l4dap8 {padding: 75px 0px 75px 0px;}
.brz .brz-css-l4dap8 > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-l4dap8 > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}
@media (min-width:991px) {.brz .brz-css-l4dap8 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-l4dap8 {padding: 50px 15px 50px 15px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-l4dap8 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-l4dap8 {padding: 25px 15px 25px 15px;}}
@media (max-width:767px) {.brz .brz-css-l4dap8 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l4dap8 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-19bfn0p {padding: 0;}
@media (min-width:991px) {.brz .brz-css-19bfn0p {padding: 0;}
	.brz .brz-css-19bfn0p > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-19bfn0p:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-19bfn0p > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-19bfn0p:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-19bfn0p:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-2kizim {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-2kizim {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-2kizim {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-2kizim {max-width: 100%;}}
@media (min-width:991px) {.brz .brz-css-1iaoc4u:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1iaoc4u {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-1vpolef {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-1vpolef > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-1vpolef > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1vpolef > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-1vpolef > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1vpolef {min-height: auto;display: flex;}
	.brz .brz-css-1vpolef > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1vpolef {min-height: auto;display: flex;}
	.brz .brz-css-1vpolef > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-1vpolef {min-height: auto;display: flex;}
	.brz .brz-css-1vpolef > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vpolef > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
.brz .brz-css-h7depb {z-index: 10;}
.brz .brz-css-h7depb > .brz-bg {border-radius: 16px;}
.brz .brz-css-h7depb > .brz-bg > .brz-bg-color {background-color: rgba(255,255,255,1);}
@media (min-width:991px) {.brz .brz-css-h7depb {margin: 0;z-index: 10;align-items: flex-start;}
	.brz .brz-css-h7depb > .brz-bg {border-radius: 16px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-h7depb:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-h7depb > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-h7depb:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-h7depb:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-h7depb > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-h7depb:hover > .brz-bg > .brz-bg-color {background-color: rgba(255,255,255,1);background-image: none;}
	.brz .brz-css-h7depb > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-h7depb:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-h7depb > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-h7depb:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-h7depb:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-h7depb {min-height: auto;display: flex;}
	.brz .brz-css-h7depb:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-h7depb:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-h7depb:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-h7depb:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-h7depb:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-h7depb > .brz-bg {border-radius: 0px;}}
@media (max-width:767px) {.brz .brz-css-h7depb > .brz-bg {border-radius: 0px;}}
.brz .brz-css-qstpsl {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-qstpsl {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-qstpsl {padding: 0;}}
.brz .brz-css-1502t3b {padding: 8px 40px 8px 40px;}
@media (min-width:991px) {.brz .brz-css-1502t3b {padding: 8px 40px 8px 40px;max-width: 100%;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1502t3b {padding: 8px 32px 8px 24px;}}
@media (max-width:767px) {.brz .brz-css-1502t3b {padding: 8px 24px 8px 16px;}}
.brz .brz-css-1au8x2i {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-1au8x2i .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-1au8x2i > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-1au8x2i > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-1au8x2i > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1au8x2i > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1au8x2i {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1au8x2i > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-1au8x2i > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1au8x2i > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-yuuu91 {flex: 1 1 26%;max-width: 26%;justify-content: center;}
.brz .brz-css-yuuu91 .brz-columns__scroll-effect {justify-content: center;}
.brz .brz-css-yuuu91 > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-yuuu91 {z-index: auto;flex: 1 1 26%;max-width: 26%;justify-content: center;}
	.brz .brz-css-yuuu91 .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-yuuu91 > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-yuuu91:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-yuuu91 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-yuuu91:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-yuuu91:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-yuuu91 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-yuuu91:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-yuuu91 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-yuuu91:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-yuuu91 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-yuuu91:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-yuuu91:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-yuuu91:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-yuuu91:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-yuuu91 {flex: 1 1 37.3%;max-width: 37.3%;}}
@media (max-width:767px) {.brz .brz-css-yuuu91 {flex: 1 1 60%;max-width: 60%;}}
.brz .brz-css-bs698n {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-bs698n {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bs698n {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-bs698n {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-bs698n {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-9koljg {margin: 0;padding: 0;}
@media (min-width:991px) {.brz .brz-css-9koljg {z-index: auto;margin: 0;border: 0px solid transparent;padding: 0;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-9koljg:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1s9eguc {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-1s9eguc .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-1s9eguc {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1s9eguc {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1s9eguc {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-41fniq {margin: 0;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-41fniq {padding: 0;margin: 0;justify-content: flex-start;position: relative;}
	.brz .brz-css-41fniq .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-41fniq {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-oykies:not(.brz-image--hovered) {max-width: 100%;}
.brz .brz-css-oykies {height: auto;border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-oykies {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-oykies .brz-picture:after {border-radius: 0px;}
.brz .brz-css-oykies .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
.brz .brz-css-oykies .brz-picture {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-oykies .brz-picture {filter: none;}
@media (min-width:991px) {.brz .brz-css-oykies {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies.brz-image--withHover img.brz-img, .brz .brz-css-oykies.brz-image--withHover img.dynamic-image, .brz .brz-css-oykies.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-oykies {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies.brz-image--withHover img.brz-img, .brz .brz-css-oykies.brz-image--withHover img.dynamic-image, .brz .brz-css-oykies.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-oykies {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oykies.brz-image--withHover img.brz-img, .brz .brz-css-oykies.brz-image--withHover img.dynamic-image, .brz .brz-css-oykies.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (min-width:991px) {.brz .brz-css-nazrvw:not(.brz-image--hovered) {max-width: 100%;}
	.brz .brz-css-nazrvw {height: auto;border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-nazrvw:hover {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-nazrvw .brz-picture:after {border-radius: 0px;}
	.brz .brz-css-nazrvw:hover .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
	.brz .brz-css-nazrvw .brz-picture {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-nazrvw:hover .brz-picture {filter: none;}}
@media (min-width:991px) {.brz .brz-css-nazrvw:hover {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-nazrvw:hover .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-nazrvw:hover .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-nazrvw.brz-image--withHover img.brz-img, .brz .brz-css-nazrvw.brz-image--withHover img.dynamic-image, .brz .brz-css-nazrvw.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-1bzmgnn.brz-hover-animation__container {max-width: 100%;}
@media (min-width:991px) {.brz .brz-css-1e0w3a6.brz-hover-animation__container {max-width: 100%;}}
.brz .brz-css-fg2kyn {padding-top: 28.8003%;}
.brz .brz-css-fg2kyn > .brz-img {position: absolute;width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-fg2kyn {padding-top: 28.7989%;}}
@media (max-width:767px) {.brz .brz-css-fg2kyn {padding-top: 28.7991%;}}
.brz .brz-css-zjrtm2 {padding-top: 0;}
.brz .brz-css-zjrtm2 > .brz-img {position: inherit;}
@media (min-width:991px) {.brz .brz-css-zjrtm2 {padding-top: 0;}
	.brz .brz-css-zjrtm2 > .brz-img {position: inherit;width: 100%;}}
.brz .brz-css-gj9gnp {width: 656.04px;height: 188.94px;margin-left: -186.32px;margin-top: 0px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-gj9gnp {width: 393.51px;height: 113.33px;margin-left: -111.76px;}}
@media (max-width:767px) {.brz .brz-css-gj9gnp {width: 433.33px;height: 124.8px;margin-left: -123.06px;}}
.brz .brz-css-ewrd9 {width: 100%;height: auto;margin-left: auto;margin-top: auto;}
@media (min-width:991px) {.brz .brz-css-ewrd9 {width: 100%;height: auto;margin-left: auto;margin-top: auto;}}
.brz .brz-css-d67m55 {flex: 1 1 67.3%;max-width: 67.3%;justify-content: center;}
.brz .brz-css-d67m55 .brz-columns__scroll-effect {justify-content: center;}
.brz .brz-css-d67m55 > .brz-bg {margin: 0px 32px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-d67m55 {z-index: auto;flex: 1 1 67.3%;max-width: 67.3%;justify-content: center;}
	.brz .brz-css-d67m55 .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-d67m55 > .brz-bg {margin: 0px 32px 0px 0px;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-d67m55:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-d67m55 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-d67m55:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-d67m55:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-d67m55 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-d67m55:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-d67m55 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-d67m55:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-d67m55 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-d67m55:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-d67m55:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-d67m55:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-d67m55:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-d67m55 {flex: 1 1 61.3%;max-width: 61.3%;}
	.brz .brz-css-d67m55 > .brz-bg {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-d67m55 {flex: 1 1 40%;max-width: 40%;}
	.brz .brz-css-d67m55 > .brz-bg {margin: 0;}}
.brz .brz-css-1uiky3m {margin: 0px 32px 0px 0px;padding: 0px 0px 0px 16px;}
@media (min-width:991px) {.brz .brz-css-1uiky3m {z-index: auto;margin: 0px 32px 0px 0px;border: 0px solid transparent;padding: 0px 0px 0px 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1uiky3m:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1uiky3m {margin: 0;padding: 0px 0px 0px 5px;}}
@media (max-width:767px) {.brz .brz-css-1uiky3m {margin: 0;padding: 0px 8px 0px 0px;}}
.brz .brz-css-idtdx9 {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-idtdx9 {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;position: relative;}
	.brz .brz-css-idtdx9 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-idtdx9 {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-idtdx9 {padding: 0;margin: 5px 0px 5px 0px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-idtdx9 {display: none;}}
@media (max-width:767px) {.brz .brz-css-idtdx9 {padding: 0;margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-idtdx9 {display: none;}}
@media (min-width:991px) {.brz .brz-css-o30vek .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-o30vek .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-o30vek .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-o30vek .brz-mm-menu__icon {display: flex;font-size: 18px;}
	.brz .brz-css-o30vek .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-o30vek .brz-menu {display: none;}}
@media (max-width:767px) {.brz .brz-css-o30vek .brz-mm-menu__icon {display: flex;font-size: 18px;}
	.brz .brz-css-o30vek .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-o30vek .brz-menu {display: none;}}
@media (min-width:991px) {.brz .brz-css-bkwmf4 .brz-mm-menu__icon {font-size: 18px;}
	.brz .brz-css-bkwmf4 .brz-mm-menu__icon {color: rgba(51,51,51,1);}}
@media (min-width:991px) {.brz .brz-css-bkwmf4 .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-bkwmf4:hover .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-bkwmf4 .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bkwmf4 .brz-mm-menu__icon {font-size: 38px;}
	.brz .brz-css-bkwmf4 .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
@media (max-width:767px) {.brz .brz-css-bkwmf4 .brz-mm-menu__icon {font-size: 32px;}
	.brz .brz-css-bkwmf4 .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
.brz .brz-css-8iad6q .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -5px 0px -5px;}
.brz .brz-css-8iad6q .brz-menu__ul {color: rgba(0,0,0,1);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(0,0,0,1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 15px 0 0;}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item {color: rgba(0,0,0,1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
.brz .brz-css-8iad6q .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);border-radius: 0px;}
.brz .brz-css-8iad6q .brz-menu__sub-menu {color: rgba(255,255,255,1);background-color: rgba(51,51,51,1);box-shadow: none;}
.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(255,255,255,1);}
.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(51,51,51,1);}
.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(51,51,51,1);}
.brz .brz-css-8iad6q .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-8iad6q .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(51,51,51,1);color: rgba(255,255,255,1);}
.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {border-color: rgba(255,255,255,1);}
.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}
@media (min-width:991px) {.brz .brz-css-8iad6q .brz-menu__ul {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-8iad6q .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-8iad6q [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-8iad6q .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-8iad6q .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-8iad6q .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-8iad6q .brz-mega-menu__dropdown {display: none;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item-dropdown > .brz-a:after {border-right-style: solid;border-left-style: none;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item-dropdown .brz-menu__sub-menu {position: relative;top: auto;left: auto;transform: translate(0,0);height: 0;overflow: hidden;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item--opened > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}
	.brz .brz-css-8iad6q.brz-menu__preview .brz-menu__sub-menu .brz-menu__item > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}}
@media (max-width:767px) {.brz .brz-css-8iad6q .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-8iad6q .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-8iad6q .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-8iad6q > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-8iad6q .brz-mega-menu__dropdown {display: block;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item-dropdown > .brz-a:after {border-right-style: solid;border-left-style: none;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item-dropdown .brz-menu__sub-menu {position: relative;top: auto;left: auto;transform: translate(0,0);height: 0;overflow: hidden;}
	.brz .brz-css-8iad6q .brz-menu__sub-menu .brz-menu__item--opened > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}
	.brz .brz-css-8iad6q.brz-menu__preview .brz-menu__sub-menu .brz-menu__item > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}}
.brz .brz-css-teu6jx .brz-menu__ul {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);margin: 0px -17.5px 0px -17.5px;}
.brz .brz-css-teu6jx .brz-menu__ul {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);}
.brz .brz-css-teu6jx .brz-menu__sub-menu {color: rgba(164,248,240,1);background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu .brz-a:hover {color: rgba(164,248,240,1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(164,248,240,1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(164,248,240,1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-teu6jx .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(var(--brz-global-color3),1);color: rgba(164,248,240,1);}
.brz .brz-css-teu6jx .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {border-color: rgba(164,248,240,1);}
@media (min-width:991px) {.brz .brz-css-teu6jx .brz-menu__ul {font-size: var(--brz-ugudlcdcxlbqfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-teu6jx .brz-menu__ul {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -17.5px 0px -17.5px;}
	.brz .brz-css-teu6jx:hover .brz-menu__ul {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 15px 0 0;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);border-radius: 0px;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu:hover {color: rgba(255,255,255,1);background-color: rgba(var(--brz-global-color3),1);box-shadow: none;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
	.brz .brz-css-teu6jx .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-teu6jx:hover .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-teu6jx .brz-menu__item-dropdown .brz-menu__item:hover {background-color: rgba(var(--brz-global-color3),1);color: rgba(255,255,255,1);}
	.brz .brz-css-teu6jx .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {border-color: rgba(255,255,255,1);}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}}
@media (min-width:991px) {.brz .brz-css-teu6jx .brz-menu__ul {font-size: var(--brz-ugudlcdcxlbqfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
	.brz .brz-css-teu6jx:hover .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx:hover .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__item-dropdown .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-teu6jx [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-teu6jx [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-teu6jx > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-teu6jx > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-teu6jx > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-teu6jx .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-teu6jx .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-teu6jx .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-teu6jx .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:767px) {.brz .brz-css-teu6jx .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-teu6jx .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-teu6jx .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}}
.brz .brz-css-1t2dxpm .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
.brz .brz-css-1t2dxpm .brz-mm-navbar .brz-mm-close {color: rgba(255,255,255,1);background-color: #333;}
.brz .brz-css-1t2dxpm .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-1t2dxpm .brz-menu__item {color: rgba(255,255,255,1);border-color: rgba(85,85,85,1);}
.brz nav.brz-mm-menu.brz-css-1t2dxpm {background-color: rgba(51,51,51,.8);}
.brz .brz-css-1t2dxpm.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
.brz .brz-css-1t2dxpm .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
.brz .brz-css-1t2dxpm .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-1t2dxpm .brz-mm-navbar {color: rgba(255,255,255,1);}
.brz .brz-css-1t2dxpm .brz-menu__item.brz-mm-listitem_opened {color: rgba(255,255,255,1);}
.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1t2dxpm .brz-mm-panels > .brz-mm-panel {background-color: rgba(51,51,51,.8);}
.brz .brz-css-1t2dxpm .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(51,51,51,.8);}
.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(85,85,85,1);}
.brz .brz-css-1t2dxpm .brz-mm-listitem {border-color: rgba(85,85,85,1);}
.brz .brz-css-1t2dxpm  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(255,255,255,1);}
.brz .brz-css-1t2dxpm .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
.brz .brz-css-1t2dxpm  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
@media (min-width:991px) {.brz .brz-css-1t2dxpm .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-1t2dxpm .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1t2dxpm .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1t2dxpm {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1t2dxpm .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1t2dxpm .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1t2dxpm .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1t2dxpm .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-1t2dxpm .brz-menu__item {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-1t2dxpm .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1t2dxpm {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1t2dxpm .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1t2dxpm .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttontabletlineheight,initial) * var(--brz-buttontabletfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1t2dxpm .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-1t2dxpm .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-1t2dxpm .brz-menu__item {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-1t2dxpm .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1t2dxpm {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1t2dxpm .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1t2dxpm .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonmobilelineheight,initial) * var(--brz-buttonmobilefontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1t2dxpm .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1t2dxpm .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-1en7lv1 .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-1en7lv1 .brz-menu__item {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
.brz nav.brz-mm-menu.brz-css-1en7lv1 {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1en7lv1.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;}
.brz .brz-css-1en7lv1 .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1en7lv1 .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1en7lv1 .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1en7lv1 .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1en7lv1.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1en7lv1 .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1en7lv1 .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1en7lv1.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1en7lv1 .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1en7lv1  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1en7lv1 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1en7lv1  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1en7lv1 .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1en7lv1 .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1en7lv1 .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1en7lv1.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}}
@media (min-width:991px) {.brz .brz-css-1en7lv1 .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
	.brz .brz-css-1en7lv1 .brz-mm-navbar .brz-mm-close:hover {color: rgba(255,255,255,1);background-color: #333;}
	.brz .brz-css-1en7lv1 .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
	.brz .brz-css-1en7lv1 .brz-menu__item:hover {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
	.brz nav.brz-mm-menu.brz-css-1en7lv1 {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1en7lv1.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
	.brz .brz-css-1en7lv1:hover .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1en7lv1 .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1en7lv1 .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1en7lv1:hover .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1en7lv1.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
	.brz .brz-css-1en7lv1.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1en7lv1:hover .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1en7lv1 .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1en7lv1.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1en7lv1:hover .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1en7lv1  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);}
	.brz:hover .brz-css-1en7lv1 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1en7lv1  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}}
@media (min-width:991px) {.brz .brz-css-1en7lv1 .brz-mm-navbar .brz-mm-close:hover {transition-duration: .3s;}
	.brz .brz-css-1en7lv1 .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1en7lv1 .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1en7lv1 {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1en7lv1:hover .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1en7lv1 .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1en7lv1 .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1en7lv1 .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1en7lv1 .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1en7lv1:hover .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1en7lv1.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1en7lv1.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1en7lv1:hover .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1en7lv1.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1en7lv1:hover .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1en7lv1 .brz-menu__item {font-family: var(--brz-heading3fontfamily,initial);}
	.brz .brz-css-1en7lv1.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1en7lv1 .brz-menu__item {font-size: var(--brz-heading3tabletfontsize,initial);font-weight: var(--brz-heading3tabletfontweight,initial);font-weight: var(--brz-heading3tabletbold,initial);line-height: var(--brz-heading3tabletlineheight,initial);letter-spacing: var(--brz-heading3tabletletterspacing,initial);font-variation-settings: var(--brz-heading3tabletfontvariation,initial);font-style: var(--brz-heading3tabletitalic,initial);text-decoration: var(--brz-heading3tablettextdecoration,initial) !important;text-transform: var(--brz-heading3tablettexttransform,initial) !important;}
	.brz .brz-css-1en7lv1 .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1en7lv1 .brz-mm-navbar {font-family: var(--brz-heading3fontfamily,initial);font-size: var(--brz-heading3tabletfontsize,initial);font-weight: var(--brz-heading3tabletfontweight,initial);font-weight: var(--brz-heading3tabletbold,initial);line-height: var(--brz-heading3tabletlineheight,initial);letter-spacing: var(--brz-heading3tabletletterspacing,initial);font-variation-settings: var(--brz-heading3tabletfontvariation,initial);font-style: var(--brz-heading3tabletitalic,initial);text-decoration: var(--brz-heading3tablettextdecoration,initial) !important;text-transform: var(--brz-heading3tablettexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1en7lv1.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading3tabletlineheight,initial) * var(--brz-heading3tabletfontsize,initial) + 15px + 15px);padding-right: 20px;}}
@media (max-width:767px) {.brz .brz-css-1en7lv1 .brz-menu__item {font-family: var(--brz-heading3fontfamily,initial);}
	.brz .brz-css-1en7lv1.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}}
@media (max-width:767px) {.brz .brz-css-1en7lv1 .brz-menu__item {font-size: var(--brz-heading3mobilefontsize,initial);font-weight: var(--brz-heading3mobilefontweight,initial);font-weight: var(--brz-heading3mobilebold,initial);line-height: var(--brz-heading3mobilelineheight,initial);letter-spacing: var(--brz-heading3mobileletterspacing,initial);font-variation-settings: var(--brz-heading3mobilefontvariation,initial);font-style: var(--brz-heading3mobileitalic,initial);text-decoration: var(--brz-heading3mobiletextdecoration,initial) !important;text-transform: var(--brz-heading3mobiletexttransform,initial) !important;}
	.brz .brz-css-1en7lv1 .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1en7lv1 .brz-mm-navbar {font-family: var(--brz-heading3fontfamily,initial);font-size: var(--brz-heading3mobilefontsize,initial);font-weight: var(--brz-heading3mobilefontweight,initial);font-weight: var(--brz-heading3mobilebold,initial);line-height: var(--brz-heading3mobilelineheight,initial);letter-spacing: var(--brz-heading3mobileletterspacing,initial);font-variation-settings: var(--brz-heading3mobilefontvariation,initial);font-style: var(--brz-heading3mobileitalic,initial);text-decoration: var(--brz-heading3mobiletextdecoration,initial) !important;text-transform: var(--brz-heading3mobiletexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1en7lv1.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading3mobilelineheight,initial) * var(--brz-heading3mobilefontsize,initial) + 15px + 15px);padding-right: 20px;}}
.brz .brz-css-7jstn6 {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-7jstn6 {display: none;}}
@media (min-width:991px) {.brz .brz-css-7jstn6 {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;position: relative;}
	.brz .brz-css-7jstn6 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-7jstn6 {display: flex;display: none;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-7jstn6 {padding: 0;margin: 5px 0px 5px 0px;}}
@media (max-width:767px) {.brz .brz-css-7jstn6 {padding: 0;margin: 10px 0px 10px 0px;}}
@media (min-width:991px) {.brz .brz-css-xh7cfw .brz-mm-menu__icon {font-size: 18px;}
	.brz .brz-css-xh7cfw .brz-mm-menu__icon {color: rgba(51,51,51,1);}}
@media (min-width:991px) {.brz .brz-css-xh7cfw .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-xh7cfw:hover .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-xh7cfw .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-xh7cfw .brz-mm-menu__icon {font-size: 28px;}
	.brz .brz-css-xh7cfw .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
@media (max-width:767px) {.brz .brz-css-xh7cfw .brz-mm-menu__icon {font-size: 28px;}
	.brz .brz-css-xh7cfw .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
.brz .brz-css-13m1mto .brz-menu__ul {font-family: var(--brz-heading4fontfamily,initial);margin: 0px -17.5px 0px -17.5px;}
.brz .brz-css-13m1mto .brz-menu__ul {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 4px 0 0;}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-13m1mto .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);}
.brz .brz-css-13m1mto .brz-menu__sub-menu {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-13m1mto .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-13m1mto .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-13m1mto .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-13m1mto .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-13m1mto .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-13m1mto .brz-menu__ul {font-size: var(--brz-heading4fontsize,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);line-height: var(--brz-heading4lineheight,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 20px;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-13m1mto .brz-menu__ul {font-family: var(--brz-heading4fontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -17.5px 0px -17.5px;}
	.brz .brz-css-13m1mto:hover .brz-menu__ul {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 4px 0 0;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);border-radius: 0px;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu:hover {color: rgba(255,255,255,1);background-color: rgba(var(--brz-global-color3),1);box-shadow: none;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
	.brz .brz-css-13m1mto .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-13m1mto:hover .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-13m1mto .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-13m1mto .brz-menu__item-dropdown .brz-menu__item:hover {background-color: rgba(var(--brz-global-color3),1);color: rgba(255,255,255,1);}
	.brz .brz-css-13m1mto .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {border-color: rgba(255,255,255,1);}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}}
@media (min-width:991px) {.brz .brz-css-13m1mto .brz-menu__ul {font-size: var(--brz-heading4fontsize,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);line-height: var(--brz-heading4lineheight,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
	.brz .brz-css-13m1mto:hover .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 20px;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto:hover .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__item-dropdown .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-13m1mto [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-13m1mto [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-13m1mto > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-13m1mto > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-13m1mto > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-13m1mto .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-13m1mto .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-13m1mto .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-13m1mto .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:767px) {.brz .brz-css-13m1mto .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-13m1mto .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-13m1mto .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}}
.brz .brz-css-1pfv2km .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-1pfv2km .brz-menu__item {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
.brz nav.brz-mm-menu.brz-css-1pfv2km {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1pfv2km.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;}
.brz .brz-css-1pfv2km .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1pfv2km .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1pfv2km .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1pfv2km .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1pfv2km.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1pfv2km .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1pfv2km .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1pfv2km.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1pfv2km .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1pfv2km  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1pfv2km .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1pfv2km  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1pfv2km .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1pfv2km .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1pfv2km .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1pfv2km .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1pfv2km.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}}
@media (min-width:991px) {.brz .brz-css-1pfv2km .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
	.brz .brz-css-1pfv2km .brz-mm-navbar .brz-mm-close:hover {color: rgba(255,255,255,1);background-color: #333;}
	.brz .brz-css-1pfv2km .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
	.brz .brz-css-1pfv2km .brz-menu__item:hover {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
	.brz nav.brz-mm-menu.brz-css-1pfv2km {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1pfv2km.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
	.brz .brz-css-1pfv2km:hover .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1pfv2km .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1pfv2km .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1pfv2km:hover .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1pfv2km.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
	.brz .brz-css-1pfv2km.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1pfv2km:hover .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1pfv2km .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1pfv2km.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1pfv2km:hover .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1pfv2km  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);}
	.brz:hover .brz-css-1pfv2km .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1pfv2km  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}}
@media (min-width:991px) {.brz .brz-css-1pfv2km .brz-mm-navbar .brz-mm-close:hover {transition-duration: .3s;}
	.brz .brz-css-1pfv2km .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1pfv2km .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1pfv2km {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pfv2km:hover .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pfv2km .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1pfv2km .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1pfv2km .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pfv2km .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pfv2km:hover .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pfv2km.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1pfv2km.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1pfv2km:hover .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pfv2km.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pfv2km:hover .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1pfv2km .brz-menu__item {font-family: var(--brz-heading5fontfamily,initial);}
	.brz .brz-css-1pfv2km .brz-menu__item {color: rgba(108,230,216,1);}
	.brz .brz-css-1pfv2km.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}
	.brz .brz-css-1pfv2km .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(108,230,216,1);}
	.brz .brz-css-1pfv2km .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(108,230,216,1);}
	.brz .brz-css-1pfv2km .brz-mm-navbar {color: rgba(108,230,216,1);}
	.brz .brz-css-1pfv2km .brz-menu__item.brz-mm-listitem_opened {color: rgba(108,230,216,1);}
	.brz .brz-css-1pfv2km  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1pfv2km .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1pfv2km  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1pfv2km .brz-menu__item {font-size: var(--brz-heading5tabletfontsize,initial);font-weight: var(--brz-heading5tabletfontweight,initial);font-weight: var(--brz-heading5tabletbold,initial);line-height: var(--brz-heading5tabletlineheight,initial);letter-spacing: var(--brz-heading5tabletletterspacing,initial);font-variation-settings: var(--brz-heading5tabletfontvariation,initial);font-style: var(--brz-heading5tabletitalic,initial);text-decoration: var(--brz-heading5tablettextdecoration,initial) !important;text-transform: var(--brz-heading5tablettexttransform,initial) !important;}
	.brz .brz-css-1pfv2km .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1pfv2km .brz-mm-menu__item__icon {margin: 0 4px 0 0;font-size: 20px;}
	.brz .brz-css-1pfv2km .brz-mm-navbar {font-family: var(--brz-heading5fontfamily,initial);font-size: var(--brz-heading5tabletfontsize,initial);font-weight: var(--brz-heading5tabletfontweight,initial);font-weight: var(--brz-heading5tabletbold,initial);line-height: var(--brz-heading5tabletlineheight,initial);letter-spacing: var(--brz-heading5tabletletterspacing,initial);font-variation-settings: var(--brz-heading5tabletfontvariation,initial);font-style: var(--brz-heading5tabletitalic,initial);text-decoration: var(--brz-heading5tablettextdecoration,initial) !important;text-transform: var(--brz-heading5tablettexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1pfv2km.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading5tabletlineheight,initial) * var(--brz-heading5tabletfontsize,initial) + 15px + 15px);padding-right: 20px;}}
@media (max-width:767px) {.brz .brz-css-1pfv2km .brz-menu__item {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);}
	.brz .brz-css-1pfv2km .brz-menu__item {color: rgba(130,247,234,1);}
	.brz .brz-css-1pfv2km.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}
	.brz .brz-css-1pfv2km .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(130,247,234,1);}
	.brz .brz-css-1pfv2km .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(130,247,234,1);}
	.brz .brz-css-1pfv2km .brz-mm-navbar {color: rgba(130,247,234,1);}
	.brz .brz-css-1pfv2km .brz-menu__item.brz-mm-listitem_opened {color: rgba(130,247,234,1);}
	.brz .brz-css-1pfv2km  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(255,255,255,1);}
	.brz .brz-css-1pfv2km .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
	.brz .brz-css-1pfv2km  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}}
@media (max-width:767px) {.brz .brz-css-1pfv2km .brz-menu__item {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}
	.brz .brz-css-1pfv2km .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1pfv2km .brz-mm-menu__item__icon {margin: 0 4px 0 0;font-size: 20px;}
	.brz .brz-css-1pfv2km .brz-mm-navbar {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1pfv2km.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-ugudlcdcxlbqmobilelineheight,initial) * var(--brz-ugudlcdcxlbqmobilefontsize,initial) + 15px + 15px);padding-right: 20px;}}
.brz .brz-css-1rk9xo9 {flex: 1 1 6.7%;max-width: 6.7%;justify-content: center;}
.brz .brz-css-1rk9xo9 .brz-columns__scroll-effect {justify-content: center;}
@media (min-width:991px) {.brz .brz-css-1rk9xo9 {z-index: auto;flex: 1 1 6.7%;max-width: 6.7%;justify-content: center;}
	.brz .brz-css-1rk9xo9 .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-1rk9xo9 > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-1rk9xo9:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1rk9xo9 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1rk9xo9:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1rk9xo9:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1rk9xo9 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1rk9xo9:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1rk9xo9 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1rk9xo9:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1rk9xo9 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1rk9xo9:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1rk9xo9:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1rk9xo9:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1rk9xo9:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1rk9xo9 > * {display: none;}
	.brz .brz-css-1rk9xo9 > .brz-column__items {display: none;}}
@media (max-width:767px) {.brz .brz-css-1rk9xo9 {flex: 1 1 100%;max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-1rk9xo9 > * {display: none;}
	.brz .brz-css-1rk9xo9 > .brz-column__items {display: none;}}
.brz .brz-css-c6h42g {padding: 0;}
@media (min-width:991px) {.brz .brz-css-c6h42g {z-index: auto;margin: 0;border: 0px solid transparent;padding: 0;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-c6h42g:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-c6h42g {padding: 5px 15px 5px 15px;}}
.brz .brz-css-1l081p9 {margin: 0;}
.brz .brz-css-1l081p9 .brz-wrapper-transform {--transform-flipHorizontal: 1;--transform-flipVertical: 1;--transform-anchor-pointX: center;--transform-anchor-pointY: center;transform: scaleX(calc(var(--transform-flipHorizontal) * 1)) scaleY(calc(var(--transform-flipVertical) * 1));transform-origin: var(--transform-anchor-pointY) var(--transform-anchor-pointX);}
@media (min-width:991px) {.brz .brz-css-1l081p9 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1l081p9 .brz-wrapper-transform {--transform-flipHorizontal: 1;--transform-flipVertical: 1;--transform-anchor-pointX: center;--transform-anchor-pointY: center;transform: scaleX(calc(var(--transform-flipHorizontal) * 1)) scaleY(calc(var(--transform-flipVertical) * 1));transform-origin: var(--transform-anchor-pointY) var(--transform-anchor-pointX);}}
@media (min-width:991px) {.brz .brz-css-1l081p9 {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1l081p9 {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-1l081p9 {margin: 10px 0px 10px 0px;}}
.brz .brz-css-a1usu8 {flex-direction: row;}
.brz .brz-css-a1usu8 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}
.brz .brz-css-1xajxap .brz-icon__container {margin-left: auto;margin-right: 2px;}
@media (min-width:991px) {.brz .brz-css-1xajxap {flex-direction: row;}
	.brz .brz-css-1xajxap .brz-icon__container {margin-left: auto;margin-right: 2px;align-items: flex-start;}}
.brz .brz-css-8hiy7p {font-size: 48px;padding: 0px;border-radius: 0;stroke-width: 1;}
.brz .brz-css-8hiy7p {color: rgba(var(--brz-global-color3),1);border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}
.brz .brz-css-8hiy7p .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-8hiy7p {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-8hiy7p .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-8hiy7p:hover {color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-8hiy7p:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-8hiy7p {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-8hiy7p .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:767px) {.brz .brz-css-8hiy7p {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-8hiy7p .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-1jhn46a {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-1jhn46a {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-1jhn46a:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1jhn46a:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1jhn46a:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-17x399q {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-y38r88 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-nU99y {margin-top: 0px !important;margin-bottom: 0px !important;text-align: justify !important;font-family: "Comfortaa",display !important;font-size: 16px;line-height: 1.7;font-weight: 400;letter-spacing: -.2px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;}
@media (min-width:991px) {.brz .brz-css-nU99y {margin-top: 0px !important;margin-bottom: 0px !important;text-align: justify !important;font-family: "Comfortaa",display !important;font-size: 16px;line-height: 1.7;font-weight: 400;letter-spacing: -.2px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
.brz .brz-css-j22a30 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-j22a30 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-j22a30 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-j22a30 {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-j22a30 {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-j22a30 {margin: 10px 0px 10px 0px;}}
.brz .brz-css-1nobro9 {width: 100%;min-height: 100%;}
.brz .brz-css-1nobro9:before {border-radius: 0px;}
.brz .brz-css-1nobro9:before {box-shadow: none;border: 0px solid rgba(220,222,225,1);}
.brz .brz-css-1nobro9 .brz-embed-content {padding: 0;border-radius: 0px;overflow: hidden;}
.brz .brz-css-1nobro9 .brz-embed-content {background-color: rgba(var(--brz-global-color2),0);background-image: none;}
@media (min-width:991px) {.brz .brz-css-1nobro9:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1nobro9:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1nobro9:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-r1fo27 {width: 100%;min-height: 100%;}
	.brz .brz-css-r1fo27:before {border-radius: 0px;}
	.brz .brz-css-r1fo27:hover:before {box-shadow: none;border: 0px solid rgba(220,222,225,1);}
	.brz .brz-css-r1fo27 .brz-embed-content {padding: 0;border-radius: 0px;overflow: hidden;}
	.brz .brz-css-r1fo27:hover .brz-embed-content {background-color: rgba(var(--brz-global-color2),0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-r1fo27:hover:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-icza6t {z-index: auto;margin: 0;}
.brz .brz-css-icza6t.brz-section .brz-section__content {min-height: auto;display: flex;}
.brz .brz-css-icza6t .brz-container {justify-content: center;}
.brz .brz-css-icza6t > .slick-slider > .brz-slick-slider__dots {color: rgba(0,0,0,1);}
.brz .brz-css-icza6t > .slick-slider > .brz-slick-slider__arrow {color: rgba(0,0,0,.7);}
@media (min-width:991px) {.brz .brz-css-icza6t {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-icza6t {display: block;}}
@media (max-width:767px) {.brz .brz-css-icza6t {display: block;}}
@media (min-width:991px) {.brz .brz-css-1jzivbc {z-index: auto;margin: 0;}
	.brz .brz-css-1jzivbc.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-1jzivbc .brz-container {justify-content: center;}
	.brz .brz-css-1jzivbc > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-1jzivbc > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-1jzivbc:hover {display: block;}}
.brz .brz-css-112foev {padding: 75px 0px 75px 0px;}
.brz .brz-css-112foev > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-112foev > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-112foev > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
.brz .brz-css-112foev > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}
@media (min-width:991px) {.brz .brz-css-112foev > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-112foev {padding: 50px 15px 50px 15px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-112foev > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-112foev {padding: 25px 15px 25px 15px;}}
@media (max-width:767px) {.brz .brz-css-112foev > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-112foev > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-phah2e {padding: 144px 48px 140px 48px;}
.brz .brz-css-phah2e > .brz-bg > .brz-bg-image {background-image: url("../../assets/9c20ff7aad5fdaefadab6d02da97fb9a.webp");background-position: 50% 50%;}
.brz .brz-css-phah2e > .brz-bg > .brz-bg-image:after {content: "";background-image: url("../../assets/9c20ff7aad5fdaefadab6d02da97fb9a.webp");}
.brz .brz-css-phah2e > .brz-bg > .brz-bg-color {background-color: transparent;background-image: linear-gradient(25deg,rgba(var(--brz-global-color2),1) 0%,rgba(var(--brz-global-color8),.71) 100%);}
@media (min-width:991px) {.brz .brz-css-phah2e > .brz-bg > .brz-bg-image {background-attachment: fixed;}}
@media (min-width:991px) {.brz .brz-css-phah2e {padding: 144px 48px 140px 48px;}
	.brz .brz-css-phah2e > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-phah2e:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-image {background-image: url("../../assets/9c20ff7aad5fdaefadab6d02da97fb9a.webp");filter: none;background-position: 50% 50%;display: block;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: url("../../assets/9c20ff7aad5fdaefadab6d02da97fb9a.webp");}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-color {background-color: transparent;background-image: linear-gradient(25deg,rgba(var(--brz-global-color2),1) 0%,rgba(var(--brz-global-color8),.71) 100%);}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-phah2e > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-phah2e:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-image {background-attachment: fixed;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-phah2e:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-phah2e {padding: 75px 40px 75px 40px;}}
@media (max-width:767px) {.brz .brz-css-phah2e {padding: 64px 16px 64px 16px;}}
.brz .brz-css-1q2jeb8 {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1q2jeb8 {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1q2jeb8 {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-1q2jeb8 {max-width: 100%;}}
@media (min-width:991px) {.brz .brz-css-14dqn75:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-14dqn75 {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-2u69vb {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-2u69vb > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-2u69vb > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-2u69vb > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-2u69vb > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-2u69vb {min-height: auto;display: flex;}
	.brz .brz-css-2u69vb > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-2u69vb {min-height: auto;display: flex;}
	.brz .brz-css-2u69vb > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-2u69vb {min-height: auto;display: flex;}
	.brz .brz-css-2u69vb > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2u69vb > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (min-width:991px) {.brz .brz-css-tlsm9t {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-tlsm9t > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-tlsm9t:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-tlsm9t > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-tlsm9t:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-tlsm9t:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-tlsm9t > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-tlsm9t:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-tlsm9t > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-tlsm9t:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-tlsm9t > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-tlsm9t:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-tlsm9t:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-tlsm9t {min-height: auto;display: flex;}
	.brz .brz-css-tlsm9t:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-tlsm9t:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-tlsm9t:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-tlsm9t:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-tlsm9t:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1aujj4f {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1aujj4f {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1aujj4f {padding: 0;}}
.brz .brz-css-1lmvhsw {padding: 0;}
@media (min-width:991px) {.brz .brz-css-swih1j {padding: 0;max-width: 100%;}}
.brz .brz-css-1dg6w3w {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-1dg6w3w .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-1dg6w3w > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-1dg6w3w > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-1dg6w3w > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1dg6w3w > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1dg6w3w {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1dg6w3w > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-1dg6w3w > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1dg6w3w > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-5t5w8a {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-5t5w8a > .brz-bg {margin: 0;}
.brz .brz-css-5t5w8a > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color5),.62);}
@media (min-width:991px) {.brz .brz-css-5t5w8a {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-5t5w8a .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-5t5w8a > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-5t5w8a:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-5t5w8a > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-5t5w8a:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-5t5w8a:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-5t5w8a > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-5t5w8a:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color5),.62);background-image: none;}
	.brz .brz-css-5t5w8a > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-5t5w8a:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-5t5w8a > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-5t5w8a:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-5t5w8a:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-5t5w8a:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-5t5w8a:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-fwup9k {animation-name: none;animation-duration: 1000ms;animation-delay: 1000ms;animation-iteration-count: unset;}
.brz .brz-css-12o7bs6 {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}
@media (min-width:991px) {.brz .brz-css-12o7bs6 {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}}
.brz .brz-css-1jiv5r7 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-1jiv5r7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1jiv5r7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1jiv5r7 {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1jiv5r7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1cqgkiq {margin: 0;padding: 64px;}
@media (min-width:991px) {.brz .brz-css-1cqgkiq {z-index: auto;margin: 0;border: 0px solid transparent;padding: 64px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1cqgkiq:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1cqgkiq {padding: 44px;}}
@media (max-width:767px) {.brz .brz-css-1cqgkiq {padding: 48px 32px 48px 32px;}}
.brz .brz-css-gviw7v {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-gviw7v .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-gviw7v {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-gviw7v {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-gviw7v {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-lowft {margin: 0;}
@media (min-width:991px) {.brz .brz-css-lowft {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-lowft .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-lowft {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-lowft {margin: 0px 0px 16px 0px;}}
@media (max-width:767px) {.brz .brz-css-lowft {margin: 0px 0px 24px 0px;}}
.brz .brz-css-t4mrh8 {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-3dyjyl {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-i4m2s {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading2fontfamily,initial) !important;font-size: var(--brz-heading2fontsize,initial);line-height: var(--brz-heading2lineheight,initial);font-weight: var(--brz-heading2fontweight,initial);font-weight: var(--brz-heading2bold,initial);letter-spacing: var(--brz-heading2letterspacing,initial);font-variation-settings: var(--brz-heading2fontvariation,initial);font-style: var(--brz-heading2italic,initial);text-decoration: var(--brz-heading2textdecoration,initial) !important;text-transform: var(--brz-heading2texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-i4m2s {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading2fontfamily,initial) !important;font-size: var(--brz-heading2fontsize,initial);line-height: var(--brz-heading2lineheight,initial);font-weight: var(--brz-heading2fontweight,initial);font-weight: var(--brz-heading2bold,initial);letter-spacing: var(--brz-heading2letterspacing,initial);font-variation-settings: var(--brz-heading2fontvariation,initial);font-style: var(--brz-heading2italic,initial);text-decoration: var(--brz-heading2textdecoration,initial) !important;text-transform: var(--brz-heading2texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-i4m2s {font-size: var(--brz-heading2tabletfontsize,initial);line-height: var(--brz-heading2tabletlineheight,initial);font-weight: var(--brz-heading2tabletfontweight,initial);font-weight: var(--brz-heading2tabletbold,initial);letter-spacing: var(--brz-heading2tabletletterspacing,initial);font-variation-settings: var(--brz-heading2tabletfontvariation,initial);font-style: var(--brz-heading2tabletitalic,initial);text-decoration: var(--brz-heading2tablettextdecoration,initial) !important;text-transform: var(--brz-heading2tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-i4m2s {text-align: center !important;font-size: var(--brz-heading2mobilefontsize,initial);line-height: var(--brz-heading2mobilelineheight,initial);font-weight: var(--brz-heading2mobilefontweight,initial);font-weight: var(--brz-heading2mobilebold,initial);letter-spacing: var(--brz-heading2mobileletterspacing,initial);font-variation-settings: var(--brz-heading2mobilefontvariation,initial);font-style: var(--brz-heading2mobileitalic,initial);text-decoration: var(--brz-heading2mobiletextdecoration,initial) !important;text-transform: var(--brz-heading2mobiletexttransform,initial) !important;}}
.brz .brz-css-sbh8w5 {margin: 32px 0px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-sbh8w5 {padding: 0;margin: 32px 0px 0px 0px;justify-content: center;position: relative;}
	.brz .brz-css-sbh8w5 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-sbh8w5 {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sbh8w5 {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-sbh8w5 {margin: 10px 0px 10px 0px;}}
@media (min-width:991px) {.brz .brz-css-1jxds40 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-xPMSF {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-xPMSF {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-xPMSF {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-xPMSF {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-sY6QH {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-sY6QH {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sY6QH {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-sY6QH {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-kdXZ5 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-kdXZ5 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-kdXZ5 {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-kdXZ5 {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-vMsgd {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-vMsgd {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-vMsgd {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-vMsgd {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-ecved {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-ecved {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-ecved {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-ecved {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-aE5hC {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-aE5hC {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-aE5hC {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-aE5hC {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-fwrXA {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-fwrXA {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-fwrXA {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-fwrXA {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-bgff5 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-bgff5 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bgff5 {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-bgff5 {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-zCLqE {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-zCLqE {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-zCLqE {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-zCLqE {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-vsoXX {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-vsoXX {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-vsoXX {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-vsoXX {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-xevD6 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-xevD6 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-xevD6 {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-xevD6 {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-h2gx0 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-h2gx0 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-h2gx0 {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-h2gx0 {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-dvEBB {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-dvEBB {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-dvEBB {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-dvEBB {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-bYNJc {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-bYNJc {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bYNJc {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-bYNJc {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-tPFgT {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-tPFgT {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-tPFgT {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-tPFgT {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-rSs_H {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-rSs_H {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-rSs_H {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-rSs_H {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-rJGbq {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-rJGbq {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-rJGbq {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-rJGbq {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-pHs8Q {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-pHs8Q {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-pHs8Q {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-pHs8Q {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-kHGYz {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-kHGYz {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-kHGYz {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-kHGYz {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-icO37 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-icO37 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-icO37 {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-icO37 {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
.brz .brz-css-kd0irf {z-index: auto;margin: 0;}
.brz .brz-css-kd0irf.brz-section .brz-section__content {min-height: auto;display: flex;}
.brz .brz-css-kd0irf .brz-container {justify-content: center;}
.brz .brz-css-kd0irf > .slick-slider > .brz-slick-slider__dots {color: rgba(0,0,0,1);}
.brz .brz-css-kd0irf > .slick-slider > .brz-slick-slider__arrow {color: rgba(0,0,0,.7);}
@media (min-width:991px) {.brz .brz-css-kd0irf {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-kd0irf {display: block;}}
@media (max-width:767px) {.brz .brz-css-kd0irf {display: block;}}
@media (min-width:991px) {.brz .brz-css-qeaf84 {z-index: auto;margin: 0;}
	.brz .brz-css-qeaf84.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-qeaf84 .brz-container {justify-content: center;}
	.brz .brz-css-qeaf84 > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-qeaf84 > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-qeaf84:hover {display: block;}}
.brz .brz-css-1u1eagc {padding: 75px 0px 75px 0px;}
.brz .brz-css-1u1eagc > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-1u1eagc > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}
@media (min-width:991px) {.brz .brz-css-1u1eagc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1u1eagc {padding: 50px 15px 50px 15px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1u1eagc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1u1eagc {padding: 25px 15px 25px 15px;}}
@media (max-width:767px) {.brz .brz-css-1u1eagc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1u1eagc > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1gdxim2 {padding: 116px 0px 128px 0px;}
.brz .brz-css-1gdxim2 > .brz-bg {border-width: 1px 0px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),.1);}
@media (min-width:991px) {.brz .brz-css-1gdxim2 {padding: 116px 0px 128px 0px;}
	.brz .brz-css-1gdxim2 > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-1gdxim2:hover > .brz-bg {border-width: 1px 0px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),.1);background-image: none;}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1gdxim2 > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1gdxim2:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1gdxim2:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1gdxim2 {padding: 75px 40px 35px 40px;}}
@media (max-width:767px) {.brz .brz-css-1gdxim2 {padding: 24px 24px 72px 24px;}}
.brz .brz-css-cvhhtf {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-cvhhtf {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-cvhhtf {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-cvhhtf {max-width: 100%;}}
.brz .brz-css-tm25nd {border-width: 1px 0px 0px 0px;border-style: solid;border-color: transparent;}
@media (min-width:991px) {.brz .brz-css-tm25nd:hover {border-width: 1px 0px 0px 0px;border-style: solid;border-color: transparent;}}
@media (min-width:991px) {.brz .brz-css-tm25nd {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-2tdmu5 {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-2tdmu5 > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-2tdmu5 > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-2tdmu5 > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-2tdmu5 {min-height: auto;display: flex;}
	.brz .brz-css-2tdmu5 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-2tdmu5 {min-height: auto;display: flex;}
	.brz .brz-css-2tdmu5 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-2tdmu5 {min-height: auto;display: flex;}
	.brz .brz-css-2tdmu5 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2tdmu5 > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (min-width:991px) {.brz .brz-css-u60gik {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-u60gik > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-u60gik:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-u60gik > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-u60gik:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-u60gik:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-u60gik > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-u60gik:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-u60gik > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-u60gik:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-u60gik > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-u60gik:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-u60gik:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-u60gik {min-height: auto;display: flex;}
	.brz .brz-css-u60gik:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-u60gik:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-u60gik:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-u60gik:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-u60gik:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1cwzyu7 {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1cwzyu7 {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1cwzyu7 {padding: 0;}}
.brz .brz-css-72iks1 {padding: 0;}
@media (min-width:991px) {.brz .brz-css-iy5bpn {padding: 0;max-width: 100%;}}
.brz .brz-css-snmdvf {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-snmdvf .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-snmdvf > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-snmdvf > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-snmdvf > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-snmdvf > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-snmdvf > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-snmdvf > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-snmdvf > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-snmdvf > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-snmdvf > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-snmdvf {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-snmdvf > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-snmdvf > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-snmdvf > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-snmdvf > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1rg80a8 > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1rg80a8 {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
	.brz .brz-css-1rg80a8 .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-1rg80a8 > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-1rg80a8:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1rg80a8 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1rg80a8:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1rg80a8:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1rg80a8 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1rg80a8:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1rg80a8 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1rg80a8:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1rg80a8 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1rg80a8:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1rg80a8:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1rg80a8:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1rg80a8:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-nkqxln {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-nkqxln {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-nkqxln {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-nkqxln {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-nkqxln {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1vtg2v0 {margin: 0;padding: 16px;}
@media (min-width:991px) {.brz .brz-css-1vtg2v0 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1vtg2v0:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1vtg2v0 {padding: 8px;}}
@media (max-width:767px) {.brz .brz-css-1vtg2v0 {padding: 25px 0px 0px 0px;}}
.brz .brz-css-9bmuwm {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-9bmuwm .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-9bmuwm {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-9bmuwm {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-9bmuwm {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-kuif0 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-kuif0 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-kuif0 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-kuif0 {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-kuif0 {margin: 0px 0px 8px 0px;}}
.brz .brz-css-hnubjp {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-1erwwuu {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-avgV7 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: right !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-avgV7 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: right !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-avgV7 {font-size: var(--brz-subtitletabletfontsize,initial);line-height: var(--brz-subtitletabletlineheight,initial);font-weight: var(--brz-subtitletabletfontweight,initial);font-weight: var(--brz-subtitletabletbold,initial);letter-spacing: var(--brz-subtitletabletletterspacing,initial);font-variation-settings: var(--brz-subtitletabletfontvariation,initial);font-style: var(--brz-subtitletabletitalic,initial);text-decoration: var(--brz-subtitletablettextdecoration,initial) !important;text-transform: var(--brz-subtitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-avgV7 {text-align: justify !important;font-size: var(--brz-subtitlemobilefontsize,initial);line-height: var(--brz-subtitlemobilelineheight,initial);font-weight: var(--brz-subtitlemobilefontweight,initial);font-weight: var(--brz-subtitlemobilebold,initial);letter-spacing: var(--brz-subtitlemobileletterspacing,initial);font-variation-settings: var(--brz-subtitlemobilefontvariation,initial);font-style: var(--brz-subtitlemobileitalic,initial);text-decoration: var(--brz-subtitlemobiletextdecoration,initial) !important;text-transform: var(--brz-subtitlemobiletexttransform,initial) !important;}}
.brz .brz-css-a44jyd {margin: 0;}
@media (min-width:991px) {.brz .brz-css-9hf8dj {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-9hf8dj .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-9hf8dj {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-9hf8dj {display: none;}}
.brz .brz-css-1oatros {height: 50px;}
.brz .brz-css-18vc5vh {height: 20px;}
@media (min-width:991px) {.brz .brz-css-18vc5vh {height: 20px;}}
@media (max-width:767px) {.brz .brz-css-18vc5vh {height: 15px;}}
.brz .brz-css-1vvy1um {z-index: auto;position: relative;margin: 10px 0px 10px 0px;justify-content: center;padding: 0;gap: 20px 10px;}
@media (min-width:991px) {.brz .brz-css-1vvy1um {position: relative;}
	.brz .brz-css-1vvy1um {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1vvy1um {position: relative;}
	.brz .brz-css-1vvy1um {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1vvy1um {position: relative;}
	.brz .brz-css-1vvy1um {display: flex;}}
.brz .brz-css-6yal2v {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-6yal2v {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-6yal2v {position: relative;}
	.brz .brz-css-6yal2v:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-6yal2v {justify-content: flex-start;}}
.brz .brz-css-13b0w2c {justify-content: center;padding: 0;gap: 20px 10px;}
.brz .brz-css-io0kr2 {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-io0kr2 {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-io0kr2 {justify-content: flex-start;}}
.brz .brz-css-1cvvmts.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1cvvmts.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1cvvmts.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1cvvmts.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
@media (min-width:991px) {.brz .brz-css-1cvvmts.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1cvvmts.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1cvvmts.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1cvvmts.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1cvvmts.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1cvvmts.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1cvvmts.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1cvvmts.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1cvvmts.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1cvvmts.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:767px) {.brz .brz-css-1cvvmts.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1cvvmts.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1cvvmts.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1cvvmts.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1cvvmts.brz-back-pulse:before {animation-duration: .6s;}}
.brz .brz-css-a0tp39.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-a0tp39.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-a0tp39.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-a0tp39.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-a0tp39.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-a0tp39.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-a0tp39.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-a0tp39.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-a0tp39.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-a0tp39.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-a0tp39.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-a0tp39.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-a0tp39.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-b5dipk.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 0;padding: 14px 42px 14px 42px;padding: 14px 42px;flex-flow: row-reverse nowrap;}
.brz .brz-css-b5dipk.brz-btn {display: flex;color: rgba(var(--brz-global-color8),1);border: 2px solid rgba(var(--brz-global-color3),1);box-shadow: none;}
.brz .brz-css-b5dipk.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-b5dipk.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-b5dipk.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-b5dipk:after {height: unset;}
.brz .brz-css-b5dipk .brz-btn--story-container {border: 2px solid rgba(var(--brz-global-color3),1);flex-flow: row-reverse nowrap;border-radius: 0;}
.brz .brz-css-b5dipk .brz-btn--story-container:after {height: unset;}
@media (min-width:991px) {.brz .brz-css-b5dipk.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (min-width:991px) {.brz .brz-css-b5dipk.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-b5dipk.brz-btn.brz-btn-submit:hover {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-b5dipk.brz-btn {font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);font-size: var(--brz-buttontabletfontsize,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-b5dipk.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-b5dipk.brz-btn {font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);font-size: var(--brz-buttonmobilefontsize,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:767px) {.brz .brz-css-b5dipk.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-b5dipk .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-zgvzlb.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-zgvzlb.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-zgvzlb.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-zgvzlb.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-zgvzlb.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-zgvzlb .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-zgvzlb.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-zgvzlb.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-zgvzlb.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-zgvzlb.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-zgvzlb.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-zgvzlb:after {height: unset;}
	.brz .brz-css-zgvzlb .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-zgvzlb .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-zgvzlb.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-zgvzlb.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-zgvzlb.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-zgvzlb .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-zgvzlb.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-zgvzlb.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-z47noj {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-z47noj {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-z47noj {position: relative;}
	.brz .brz-css-z47noj:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-z47noj {justify-content: flex-start;}}
.brz .brz-css-1oa5j7y {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1oa5j7y {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-1oa5j7y {justify-content: flex-start;}}
.brz .brz-css-ry3woq.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-ry3woq.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-ry3woq.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-ry3woq.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-ry3woq.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-ry3woq.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-ry3woq.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-ry3woq.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-ry3woq.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-ry3woq.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-ry3woq.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-ry3woq.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-ry3woq.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-r7my7g.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-r7my7g.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-r7my7g.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-r7my7g.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-r7my7g.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-r7my7g .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-r7my7g.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-r7my7g.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-r7my7g.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-r7my7g.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-r7my7g.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-r7my7g:after {height: unset;}
	.brz .brz-css-r7my7g .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-r7my7g .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-r7my7g.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-r7my7g.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-r7my7g.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-r7my7g .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-r7my7g.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-r7my7g.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1oj9yue {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1oj9yue {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1oj9yue {position: relative;}
	.brz .brz-css-1oj9yue:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1oj9yue {justify-content: flex-start;}}
.brz .brz-css-17z8rnh {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-17z8rnh {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-17z8rnh {justify-content: flex-start;}}
.brz .brz-css-1ul4g6z.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1ul4g6z.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-1ul4g6z.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1ul4g6z.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-1ul4g6z.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1ul4g6z.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-1ul4g6z.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1ul4g6z.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-1ul4g6z.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1ul4g6z.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1ul4g6z.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1ul4g6z.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1ul4g6z.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1pxnmd1.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-1pxnmd1.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1pxnmd1.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1pxnmd1.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1pxnmd1.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1pxnmd1 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-1pxnmd1.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1pxnmd1.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-1pxnmd1.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1pxnmd1.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1pxnmd1.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1pxnmd1:after {height: unset;}
	.brz .brz-css-1pxnmd1 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1pxnmd1 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1pxnmd1.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pxnmd1.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pxnmd1.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1pxnmd1 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1pxnmd1.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1pxnmd1.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1kfn6i7 {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1kfn6i7 {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1kfn6i7 {position: relative;}
	.brz .brz-css-1kfn6i7:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1kfn6i7 {justify-content: flex-start;}}
.brz .brz-css-y5gsi6 {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-y5gsi6 {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-y5gsi6 {justify-content: flex-start;}}
.brz .brz-css-p2w8vq.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-p2w8vq.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-p2w8vq.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-p2w8vq.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-p2w8vq.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-p2w8vq.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-p2w8vq.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-p2w8vq.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-p2w8vq.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-p2w8vq.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-p2w8vq.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-p2w8vq.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-p2w8vq.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-486v1z.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-486v1z.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-486v1z.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-486v1z.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-486v1z.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-486v1z .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-486v1z.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-486v1z.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-486v1z.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-486v1z.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-486v1z.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-486v1z:after {height: unset;}
	.brz .brz-css-486v1z .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-486v1z .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-486v1z.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-486v1z.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-486v1z.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-486v1z .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-486v1z.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-486v1z.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1n6tvm3 {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1n6tvm3 {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1n6tvm3 {position: relative;}
	.brz .brz-css-1n6tvm3:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1n6tvm3 {justify-content: flex-start;}}
.brz .brz-css-e00hn0 {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-e00hn0 {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-e00hn0 {justify-content: flex-start;}}
.brz .brz-css-3j3dtw.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-3j3dtw.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-3j3dtw.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-3j3dtw.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-3j3dtw.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-3j3dtw.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-3j3dtw.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-3j3dtw.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-3j3dtw.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-3j3dtw.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-3j3dtw.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-3j3dtw.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-3j3dtw.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-16hs2ni.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-16hs2ni.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-16hs2ni.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-16hs2ni.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-16hs2ni.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-16hs2ni .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-16hs2ni.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-16hs2ni.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-16hs2ni.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-16hs2ni.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-16hs2ni.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-16hs2ni:after {height: unset;}
	.brz .brz-css-16hs2ni .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-16hs2ni .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-16hs2ni.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-16hs2ni.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-16hs2ni.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-16hs2ni .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-16hs2ni.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-16hs2ni.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-ogcnkf > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-ogcnkf {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
	.brz .brz-css-ogcnkf .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-ogcnkf > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-ogcnkf:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-ogcnkf > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-ogcnkf:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-ogcnkf:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-ogcnkf > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-ogcnkf:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-ogcnkf > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-ogcnkf:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-ogcnkf > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-ogcnkf:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-ogcnkf:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ogcnkf:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ogcnkf:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-q4mwko {margin: 0;padding: 16px;}
@media (min-width:991px) {.brz .brz-css-q4mwko {z-index: auto;margin: 0;border: 0px solid transparent;padding: 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-q4mwko:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-q4mwko {padding: 8px;}}
@media (max-width:767px) {.brz .brz-css-q4mwko {padding: 25px 0px 0px 0px;}}
.brz .brz-css-2ev8yp {margin: 0;}
@media (min-width:991px) {.brz .brz-css-2ev8yp {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-2ev8yp .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-2ev8yp {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-2ev8yp {margin: 0px 0px 8px 0px;}}
@media (min-width:991px) {.brz .brz-css-bslr0s {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-lwMzU {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-lwMzU {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-lwMzU {font-size: var(--brz-subtitletabletfontsize,initial);line-height: var(--brz-subtitletabletlineheight,initial);font-weight: var(--brz-subtitletabletfontweight,initial);font-weight: var(--brz-subtitletabletbold,initial);letter-spacing: var(--brz-subtitletabletletterspacing,initial);font-variation-settings: var(--brz-subtitletabletfontvariation,initial);font-style: var(--brz-subtitletabletitalic,initial);text-decoration: var(--brz-subtitletablettextdecoration,initial) !important;text-transform: var(--brz-subtitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-lwMzU {text-align: justify !important;font-size: var(--brz-subtitlemobilefontsize,initial);line-height: var(--brz-subtitlemobilelineheight,initial);font-weight: var(--brz-subtitlemobilefontweight,initial);font-weight: var(--brz-subtitlemobilebold,initial);letter-spacing: var(--brz-subtitlemobileletterspacing,initial);font-variation-settings: var(--brz-subtitlemobilefontvariation,initial);font-style: var(--brz-subtitlemobileitalic,initial);text-decoration: var(--brz-subtitlemobiletextdecoration,initial) !important;text-transform: var(--brz-subtitlemobiletexttransform,initial) !important;}}
.brz .brz-css-1f8myrk {margin: 20px 0px 8px -2px;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-1f8myrk {z-index: auto;position: relative;margin: 20px 0px 8px -2px;justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1f8myrk {position: relative;}
	.brz .brz-css-1f8myrk:hover {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1f8myrk {margin: 8px 0px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1f8myrk {margin: 0;}}
.brz .brz-css-1aihhtr {justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-1aihhtr {justify-content: flex-start;padding: 0;gap: 20px 10px;}}
.brz .brz-css-194nueb {font-size: 48px;padding: 0px;border-radius: 0;stroke-width: 1;}
.brz .brz-css-194nueb {color: rgba(var(--brz-global-color3),1);border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}
.brz .brz-css-194nueb .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-194nueb {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-194nueb .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-194nueb:hover {color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-194nueb:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-194nueb {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-194nueb .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:767px) {.brz .brz-css-194nueb {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-194nueb .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-1kgpxwi {font-size: 32px;padding: 14px;border-radius: 100px;}
.brz .brz-css-1kgpxwi {color: rgba(var(--brz-global-color2),1);background-color: rgba(var(--brz-global-color7),.12);}
.brz .brz-css-1kgpxwi .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1kgpxwi {font-size: 32px;padding: 14px;border-radius: 100px;stroke-width: 1;}
	.brz .brz-css-1kgpxwi:hover {color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(var(--brz-global-color3),1);box-shadow: none;background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-1kgpxwi:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (min-width:991px) {.brz .brz-css-1kgpxwi:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1kgpxwi:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1kgpxwi {font-size: 14px;}}
@media (max-width:767px) {.brz .brz-css-1kgpxwi {font-size: 27px;}}
.brz .brz-css-32ywyv {font-size: 32px;padding: 14px;border-radius: 100px;}
.brz .brz-css-32ywyv {color: rgba(var(--brz-global-color2),1);background-color: rgba(var(--brz-global-color7),.13);}
.brz .brz-css-32ywyv .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-32ywyv {font-size: 32px;padding: 14px;border-radius: 100px;stroke-width: 1;}
	.brz .brz-css-32ywyv:hover {color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(var(--brz-global-color3),1);box-shadow: none;background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-32ywyv:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (min-width:991px) {.brz .brz-css-32ywyv:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-32ywyv:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-32ywyv {font-size: 14px;}}
@media (max-width:767px) {.brz .brz-css-32ywyv {font-size: 26px;}}
@media (min-width:991px) {.brz .brz-css-bj21vk {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-bj21vk .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-bj21vk {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1s6iklb {height: 10px;}
@media (min-width:991px) {.brz .brz-css-1s6iklb {height: 10px;}}
@media (max-width:767px) {.brz .brz-css-1s6iklb {height: 15px;}}
.brz .brz-css-zn2o2 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-zn2o2 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-zn2o2 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-zn2o2 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-nqhvk6 {flex-direction: row;}
.brz .brz-css-nqhvk6 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}
.brz .brz-css-fp6t55 .brz-icon__container {margin-left: auto;margin-right: 20px;}
@media (min-width:991px) {.brz .brz-css-fp6t55 {flex-direction: row;}
	.brz .brz-css-fp6t55 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}}
@media (max-width:767px) {.brz .brz-css-fp6t55 .brz-icon__container {margin-left: auto;margin-right: 12px;}}
.brz .brz-css-1avk5dr {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-1avk5dr {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-1avk5dr:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1avk5dr:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1avk5dr:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-16t8pfx {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-sGE8L {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-sGE8L {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sGE8L {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-sGE8L {text-align: justify !important;font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
.brz .brz-css-tb1f9i {margin: 2px 0px 8px 0px;}
@media (min-width:991px) {.brz .brz-css-tb1f9i {padding: 0;margin: 2px 0px 8px 0px;justify-content: center;position: relative;}
	.brz .brz-css-tb1f9i .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-tb1f9i {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-tb1f9i {margin: 2px 0px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-tb1f9i {margin: 2px 0px 0px 0px;}}
.brz .brz-css-yudjf0 .brz-icon__container {margin-left: auto;margin-right: 20px;}
@media (min-width:991px) {.brz .brz-css-yudjf0 {flex-direction: row;}
	.brz .brz-css-yudjf0 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}}
@media (max-width:767px) {.brz .brz-css-yudjf0 .brz-icon__container {margin-left: auto;margin-right: 12px;}}
.brz .brz-css-1eifw7k {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-1eifw7k {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-1eifw7k:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1eifw7k:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1eifw7k:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-1qbv217 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-vydA7 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-vydA7 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-vydA7 {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-vydA7 {text-align: justify !important;font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
.brz .brz-css-9plwpn {margin: 2px 0px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-9plwpn {padding: 0;margin: 2px 0px 0px 0px;justify-content: center;position: relative;}
	.brz .brz-css-9plwpn .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-9plwpn {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-9plwpn {margin: 8px 0px 0px 0px;}}
@media (min-width:991px) {.brz .brz-css-17dn06g {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-siudR {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-siudR {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-siudR {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-siudR {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-uqyua6 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-uqyua6 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-uqyua6 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-hpna1y {height: 46px;}
@media (min-width:991px) {.brz .brz-css-hpna1y {height: 46px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-hpna1y {height: 75px;}}
@media (max-width:767px) {.brz .brz-css-hpna1y {height: 45px;}}
.brz .brz-css-14ulx76 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-14ulx76 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-14ulx76 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-14ulx76 {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-8j5v47 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-zC7Kb {margin-top: 0px !important;margin-bottom: 0px !important;text-align: center !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-zC7Kb {margin-top: 0px !important;margin-bottom: 0px !important;text-align: center !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-zC7Kb {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-zC7Kb {font-family: var(--brz-u9xihr8qxhssfontfamily,initial) !important;font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;}}</style><style class="brz-style brz-project__style-palette">.brz .brz-cp-color1, .brz .brz-bcp-color1 {color: rgb(var(--brz-global-color1));}
.brz .brz-bgp-color1 {background-color: rgb(var(--brz-global-color1));}
.brz .brz-cp-color2, .brz .brz-bcp-color2 {color: rgb(var(--brz-global-color2));}
.brz .brz-bgp-color2 {background-color: rgb(var(--brz-global-color2));}
.brz .brz-cp-color3, .brz .brz-bcp-color3 {color: rgb(var(--brz-global-color3));}
.brz .brz-bgp-color3 {background-color: rgb(var(--brz-global-color3));}
.brz .brz-cp-color4, .brz .brz-bcp-color4 {color: rgb(var(--brz-global-color4));}
.brz .brz-bgp-color4 {background-color: rgb(var(--brz-global-color4));}
.brz .brz-cp-color5, .brz .brz-bcp-color5 {color: rgb(var(--brz-global-color5));}
.brz .brz-bgp-color5 {background-color: rgb(var(--brz-global-color5));}
.brz .brz-cp-color6, .brz .brz-bcp-color6 {color: rgb(var(--brz-global-color6));}
.brz .brz-bgp-color6 {background-color: rgb(var(--brz-global-color6));}
.brz .brz-cp-color7, .brz .brz-bcp-color7 {color: rgb(var(--brz-global-color7));}
.brz .brz-bgp-color7 {background-color: rgb(var(--brz-global-color7));}
.brz .brz-cp-color8, .brz .brz-bcp-color8 {color: rgb(var(--brz-global-color8));}
.brz .brz-bgp-color8 {background-color: rgb(var(--brz-global-color8));}
:root {--brz-global-color1: 114,174,172;--brz-global-color2: 50,158,155;--brz-global-color3: 114,174,172;--brz-global-color4: 184,230,225;--brz-global-color5: 84,84,84;--brz-global-color6: 238,243,240;--brz-global-color7: 103,115,108;--brz-global-color8: 255,255,255;}
:root {--brz-paragraphfontfamily: "Comfortaa",display;--brz-paragraphfontsize: 18px;--brz-paragraphfontsizesuffix: px;--brz-paragraphfontweight: 300;--brz-paragraphletterspacing: -.2px;--brz-paragraphlineheight: 1.4;--brz-paragraphfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphtabletfontsize: 17px;--brz-paragraphtabletfontweight: 100;--brz-paragraphtabletletterspacing: 0px;--brz-paragraphtabletlineheight: 1.4;--brz-paragraphtabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphmobilefontsize: 16px;--brz-paragraphmobilefontweight: 100;--brz-paragraphmobileletterspacing: -.2px;--brz-paragraphmobilelineheight: 1.4;--brz-paragraphmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphstoryfontsize: 4.14%;--brz-paragraphbold: 300;--brz-paragraphitalic: inherit;--brz-paragraphtextdecoration: inherit;--brz-paragraphtexttransform: inherit;--brz-paragraphtabletbold: 100;--brz-paragraphtabletitalic: inherit;--brz-paragraphtablettextdecoration: inherit;--brz-paragraphtablettexttransform: inherit;--brz-paragraphmobilebold: 100;--brz-paragraphmobileitalic: inherit;--brz-paragraphmobiletextdecoration: inherit;--brz-paragraphmobiletexttransform: inherit;--brz-subtitlefontfamily: "Comfortaa",display;--brz-subtitlefontsize: 24px;--brz-subtitlefontsizesuffix: px;--brz-subtitlefontweight: 400;--brz-subtitleletterspacing: -.2px;--brz-subtitlelineheight: 1.4;--brz-subtitlefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitletabletfontsize: 20px;--brz-subtitletabletfontweight: 100;--brz-subtitletabletletterspacing: 0px;--brz-subtitletabletlineheight: 1.4;--brz-subtitletabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitlemobilefontsize: 16px;--brz-subtitlemobilefontweight: 100;--brz-subtitlemobileletterspacing: 0px;--brz-subtitlemobilelineheight: 1.4;--brz-subtitlemobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitlestoryfontsize: 5.52%;--brz-subtitlebold: 400;--brz-subtitleitalic: inherit;--brz-subtitletextdecoration: inherit;--brz-subtitletexttransform: inherit;--brz-subtitletabletbold: 100;--brz-subtitletabletitalic: inherit;--brz-subtitletablettextdecoration: inherit;--brz-subtitletablettexttransform: inherit;--brz-subtitlemobilebold: 100;--brz-subtitlemobileitalic: inherit;--brz-subtitlemobiletextdecoration: inherit;--brz-subtitlemobiletexttransform: inherit;--brz-abovetitlefontfamily: "Comfortaa",display;--brz-abovetitlefontsize: 20px;--brz-abovetitlefontsizesuffix: px;--brz-abovetitlefontweight: 400;--brz-abovetitleletterspacing: 0px;--brz-abovetitlelineheight: 1.4;--brz-abovetitlefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitletabletfontsize: 18px;--brz-abovetitletabletfontweight: 400;--brz-abovetitletabletletterspacing: 0px;--brz-abovetitletabletlineheight: 1.4;--brz-abovetitletabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitlemobilefontsize: 18px;--brz-abovetitlemobilefontweight: 400;--brz-abovetitlemobileletterspacing: 0px;--brz-abovetitlemobilelineheight: 1.4;--brz-abovetitlemobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitlestoryfontsize: 4.6%;--brz-abovetitlebold: 400;--brz-abovetitleitalic: inherit;--brz-abovetitletextdecoration: inherit;--brz-abovetitletexttransform: inherit;--brz-abovetitletabletbold: 400;--brz-abovetitletabletitalic: inherit;--brz-abovetitletablettextdecoration: inherit;--brz-abovetitletablettexttransform: inherit;--brz-abovetitlemobilebold: 400;--brz-abovetitlemobileitalic: inherit;--brz-abovetitlemobiletextdecoration: inherit;--brz-abovetitlemobiletexttransform: inherit;--brz-heading1fontfamily: "Comfortaa",display;--brz-heading1fontsize: 54px;--brz-heading1fontsizesuffix: px;--brz-heading1fontweight: 700;--brz-heading1letterspacing: -.2px;--brz-heading1lineheight: 1.2;--brz-heading1fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1tabletfontsize: 40px;--brz-heading1tabletfontweight: 700;--brz-heading1tabletletterspacing: -.2px;--brz-heading1tabletlineheight: 1.2;--brz-heading1tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1mobilefontsize: 28px;--brz-heading1mobilefontweight: 700;--brz-heading1mobileletterspacing: -.8px;--brz-heading1mobilelineheight: 1.2;--brz-heading1mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1storyfontsize: 12.42%;--brz-heading1bold: 700;--brz-heading1italic: inherit;--brz-heading1textdecoration: inherit;--brz-heading1texttransform: inherit;--brz-heading1tabletbold: 700;--brz-heading1tabletitalic: inherit;--brz-heading1tablettextdecoration: inherit;--brz-heading1tablettexttransform: inherit;--brz-heading1mobilebold: 700;--brz-heading1mobileitalic: inherit;--brz-heading1mobiletextdecoration: inherit;--brz-heading1mobiletexttransform: inherit;--brz-heading2fontfamily: "Comfortaa",display;--brz-heading2fontsize: 36px;--brz-heading2fontsizesuffix: px;--brz-heading2fontweight: 700;--brz-heading2letterspacing: -.8px;--brz-heading2lineheight: 1.2;--brz-heading2fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2tabletfontsize: 34px;--brz-heading2tabletfontweight: 700;--brz-heading2tabletletterspacing: -.8px;--brz-heading2tabletlineheight: 1.2;--brz-heading2tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2mobilefontsize: 22px;--brz-heading2mobilefontweight: 700;--brz-heading2mobileletterspacing: -1px;--brz-heading2mobilelineheight: 1.2;--brz-heading2mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2storyfontsize: 8.28%;--brz-heading2bold: 700;--brz-heading2italic: inherit;--brz-heading2textdecoration: inherit;--brz-heading2texttransform: inherit;--brz-heading2tabletbold: 700;--brz-heading2tabletitalic: inherit;--brz-heading2tablettextdecoration: inherit;--brz-heading2tablettexttransform: inherit;--brz-heading2mobilebold: 700;--brz-heading2mobileitalic: inherit;--brz-heading2mobiletextdecoration: inherit;--brz-heading2mobiletexttransform: inherit;--brz-heading3fontfamily: "Comfortaa",display;--brz-heading3fontsize: 22px;--brz-heading3fontsizesuffix: px;--brz-heading3fontweight: 400;--brz-heading3letterspacing: -.6px;--brz-heading3lineheight: 1.4;--brz-heading3fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3tabletfontsize: 28px;--brz-heading3tabletfontweight: 200;--brz-heading3tabletletterspacing: -.2px;--brz-heading3tabletlineheight: 1.3;--brz-heading3tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3mobilefontsize: 20px;--brz-heading3mobilefontweight: 200;--brz-heading3mobileletterspacing: -.8px;--brz-heading3mobilelineheight: 1.3;--brz-heading3mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3storyfontsize: 5.06%;--brz-heading3bold: 400;--brz-heading3italic: inherit;--brz-heading3textdecoration: inherit;--brz-heading3texttransform: inherit;--brz-heading3tabletbold: 200;--brz-heading3tabletitalic: inherit;--brz-heading3tablettextdecoration: inherit;--brz-heading3tablettexttransform: inherit;--brz-heading3mobilebold: 200;--brz-heading3mobileitalic: inherit;--brz-heading3mobiletextdecoration: inherit;--brz-heading3mobiletexttransform: inherit;--brz-heading4fontfamily: "Comfortaa",display;--brz-heading4fontsize: 18px;--brz-heading4fontsizesuffix: px;--brz-heading4fontweight: 700;--brz-heading4letterspacing: -.2px;--brz-heading4lineheight: 1.4;--brz-heading4fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4tabletfontsize: 17px;--brz-heading4tabletfontweight: 700;--brz-heading4tabletletterspacing: 0px;--brz-heading4tabletlineheight: 1.4;--brz-heading4tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4mobilefontsize: 17px;--brz-heading4mobilefontweight: 700;--brz-heading4mobileletterspacing: 0px;--brz-heading4mobilelineheight: 1.4;--brz-heading4mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4storyfontsize: 4.14%;--brz-heading4bold: 700;--brz-heading4italic: inherit;--brz-heading4textdecoration: inherit;--brz-heading4texttransform: inherit;--brz-heading4tabletbold: 700;--brz-heading4tabletitalic: inherit;--brz-heading4tablettextdecoration: inherit;--brz-heading4tablettexttransform: inherit;--brz-heading4mobilebold: 700;--brz-heading4mobileitalic: inherit;--brz-heading4mobiletextdecoration: inherit;--brz-heading4mobiletexttransform: inherit;--brz-heading5fontfamily: "Comfortaa",display;--brz-heading5fontsize: 16px;--brz-heading5fontsizesuffix: px;--brz-heading5fontweight: 400;--brz-heading5letterspacing: 0px;--brz-heading5lineheight: 1.4;--brz-heading5fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5tabletfontsize: 16px;--brz-heading5tabletfontweight: 400;--brz-heading5tabletletterspacing: 0px;--brz-heading5tabletlineheight: 1.4;--brz-heading5tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5mobilefontsize: 16px;--brz-heading5mobilefontweight: 400;--brz-heading5mobileletterspacing: 0px;--brz-heading5mobilelineheight: 1.4;--brz-heading5mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5storyfontsize: 3.68%;--brz-heading5bold: 400;--brz-heading5italic: inherit;--brz-heading5textdecoration: inherit;--brz-heading5texttransform: inherit;--brz-heading5tabletbold: 400;--brz-heading5tabletitalic: inherit;--brz-heading5tablettextdecoration: inherit;--brz-heading5tablettexttransform: inherit;--brz-heading5mobilebold: 400;--brz-heading5mobileitalic: inherit;--brz-heading5mobiletextdecoration: inherit;--brz-heading5mobiletexttransform: inherit;--brz-heading6fontfamily: "Comfortaa",display;--brz-heading6fontsize: 14px;--brz-heading6fontsizesuffix: px;--brz-heading6fontweight: 300;--brz-heading6letterspacing: -.4px;--brz-heading6lineheight: 1.5;--brz-heading6fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6tabletfontsize: 14px;--brz-heading6tabletfontweight: 400;--brz-heading6tabletletterspacing: 0px;--brz-heading6tabletlineheight: 1.5;--brz-heading6tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6mobilefontsize: 14px;--brz-heading6mobilefontweight: 400;--brz-heading6mobileletterspacing: 0px;--brz-heading6mobilelineheight: 1.5;--brz-heading6mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6storyfontsize: 3.22%;--brz-heading6bold: 300;--brz-heading6italic: inherit;--brz-heading6textdecoration: inherit;--brz-heading6texttransform: inherit;--brz-heading6tabletbold: 400;--brz-heading6tabletitalic: inherit;--brz-heading6tablettextdecoration: inherit;--brz-heading6tablettexttransform: inherit;--brz-heading6mobilebold: 400;--brz-heading6mobileitalic: inherit;--brz-heading6mobiletextdecoration: inherit;--brz-heading6mobiletexttransform: inherit;--brz-buttonfontfamily: "Comfortaa",display;--brz-buttonfontsize: 18px;--brz-buttonfontsizesuffix: px;--brz-buttonfontweight: 700;--brz-buttonletterspacing: -.2px;--brz-buttonlineheight: 1.6;--brz-buttonfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttontabletfontsize: 16px;--brz-buttontabletfontweight: 700;--brz-buttontabletletterspacing: 0px;--brz-buttontabletlineheight: 1.6;--brz-buttontabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttonmobilefontsize: 16px;--brz-buttonmobilefontweight: 700;--brz-buttonmobileletterspacing: 0px;--brz-buttonmobilelineheight: 1.6;--brz-buttonmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttonstoryfontsize: 4.14%;--brz-buttonbold: 700;--brz-buttonitalic: inherit;--brz-buttontextdecoration: inherit;--brz-buttontexttransform: inherit;--brz-buttontabletbold: 700;--brz-buttontabletitalic: inherit;--brz-buttontablettextdecoration: inherit;--brz-buttontablettexttransform: inherit;--brz-buttonmobilebold: 700;--brz-buttonmobileitalic: inherit;--brz-buttonmobiletextdecoration: inherit;--brz-buttonmobiletexttransform: inherit;--brz-ugudlcdcxlbqfontfamily: "Comfortaa",display;--brz-ugudlcdcxlbqfontsize: 16px;--brz-ugudlcdcxlbqfontsizesuffix: px;--brz-ugudlcdcxlbqfontweight: 400;--brz-ugudlcdcxlbqletterspacing: -.2px;--brz-ugudlcdcxlbqlineheight: 1.4;--brz-ugudlcdcxlbqfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqtabletfontsize: 15px;--brz-ugudlcdcxlbqtabletfontweight: 400;--brz-ugudlcdcxlbqtabletletterspacing: -.2px;--brz-ugudlcdcxlbqtabletlineheight: 1.5;--brz-ugudlcdcxlbqtabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqmobilefontsize: 15px;--brz-ugudlcdcxlbqmobilefontweight: 400;--brz-ugudlcdcxlbqmobileletterspacing: -.2px;--brz-ugudlcdcxlbqmobilelineheight: 1.4;--brz-ugudlcdcxlbqmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqstoryfontsize: 3.68%;--brz-ugudlcdcxlbqbold: 400;--brz-ugudlcdcxlbqitalic: inherit;--brz-ugudlcdcxlbqtextdecoration: inherit;--brz-ugudlcdcxlbqtexttransform: inherit;--brz-ugudlcdcxlbqtabletbold: 400;--brz-ugudlcdcxlbqtabletitalic: inherit;--brz-ugudlcdcxlbqtablettextdecoration: inherit;--brz-ugudlcdcxlbqtablettexttransform: inherit;--brz-ugudlcdcxlbqmobilebold: 400;--brz-ugudlcdcxlbqmobileitalic: inherit;--brz-ugudlcdcxlbqmobiletextdecoration: inherit;--brz-ugudlcdcxlbqmobiletexttransform: inherit;--brz-u9xihr8qxhssfontfamily: "Comfortaa",display;--brz-u9xihr8qxhssfontsize: 14px;--brz-u9xihr8qxhssfontsizesuffix: px;--brz-u9xihr8qxhssfontweight: 700;--brz-u9xihr8qxhssletterspacing: -.2px;--brz-u9xihr8qxhsslineheight: 1.4;--brz-u9xihr8qxhssfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhsstabletfontsize: 14px;--brz-u9xihr8qxhsstabletfontweight: 400;--brz-u9xihr8qxhsstabletletterspacing: -.3px;--brz-u9xihr8qxhsstabletlineheight: 1.2;--brz-u9xihr8qxhsstabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhssmobilefontsize: 13px;--brz-u9xihr8qxhssmobilefontweight: 700;--brz-u9xihr8qxhssmobileletterspacing: -.4px;--brz-u9xihr8qxhssmobilelineheight: 1.3;--brz-u9xihr8qxhssmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhssstoryfontsize: 3.22%;--brz-u9xihr8qxhssbold: 700;--brz-u9xihr8qxhssitalic: inherit;--brz-u9xihr8qxhsstextdecoration: inherit;--brz-u9xihr8qxhsstexttransform: inherit;--brz-u9xihr8qxhsstabletbold: 400;--brz-u9xihr8qxhsstabletitalic: inherit;--brz-u9xihr8qxhsstablettextdecoration: inherit;--brz-u9xihr8qxhsstablettexttransform: inherit;--brz-u9xihr8qxhssmobilebold: 700;--brz-u9xihr8qxhssmobileitalic: inherit;--brz-u9xihr8qxhssmobiletextdecoration: inherit;--brz-u9xihr8qxhssmobiletexttransform: inherit;}</style><script src="../../assets/79ee0aed42e5d5739dfd3e832fd956d3.js" class="brz-script brz-script-preview-lib" defer="true" data-brz-group="group-1_3"></script><script src="../../assets/7bd5bb61c3a6cf37aef2a8ce02be7461.js" class="brz-script brz-script-preview-lib-pro" defer="true" data-brz-group="group-1_2"></script><script src="../../assets/246b62aec7f6f35cef79e957ee00b9c9.js" class="brz-script brz-script-preview-pro" defer="true"></script></head><body class="brz">        <div class="brz-root__container brz-reset-all brz brz-root__container-page"><section id="i26e25ead67e000e42945_iEPm8Vnj19z8" class="brz-section brz-css-bll4jo brz-css-1ijukrt"><div class="brz-section__content brz-section--boxed brz-css-l4dap8 brz-css-19bfn0p" data-brz-custom-id="pSUUPCAAu6Eb"><div class="brz-container brz-css-2kizim brz-css-1iaoc4u"><header class="brz-row__container brz-css-1vpolef brz-css-h7depb" data-brz-custom-id="ss96rS6QDuXf"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-row brz-css-qstpsl brz-css-1502t3b"><div class="brz-columns brz-css-1au8x2i brz-css-yuuu91" data-brz-custom-id="atNGDyLyrLzr"><div class="brz-column__items brz-css-bs698n brz-css-9koljg"><div id="" class="brz-css-1s9eguc brz-css-41fniq brz-wrapper"><div class="brz-image brz-css-oykies brz-css-nazrvw" data-brz-custom-id="cGmWhayyOqQf"><a class="brz-a" href="/en" target="_self" rel="noopener" data-brz-link-type="external"><picture class="brz-picture brz-d-block brz-p-relative brz-css-fg2kyn brz-css-zjrtm2"><source srcset="../../assets/c1da2483e8a99f03e1cc6722d4935ffa.webp 1x, assets/c1da2483e8a99f03e1cc6722d4935ffa.webp 2x" media="(min-width: 992px)"><source srcset="../../assets/c1da2483e8a99f03e1cc6722d4935ffa.webp 1x, assets/c1da2483e8a99f03e1cc6722d4935ffa.webp 2x" media="(min-width: 768px)"><img class="brz-img" srcset="../../assets/c1da2483e8a99f03e1cc6722d4935ffa.webp 1x, assets/c1da2483e8a99f03e1cc6722d4935ffa.webp 2x" src="../../assets/c1da2483e8a99f03e1cc6722d4935ffa.webp" alt="" title="cymta-logo-en.webp" draggable="false" loading="lazy"></picture></a></div></div></div></div><div class="brz-columns brz-css-1au8x2i brz-css-d67m55" data-brz-custom-id="aAOjA4W0xJRb"><div class="brz-column__items brz-css-bs698n brz-css-1uiky3m"><div id="" class="brz-css-1s9eguc brz-css-idtdx9 brz-wrapper"><div class="brz-menu__container brz-css-o30vek brz-css-bkwmf4" data-mmenu-id="#qZIFRiPnadng_id8899182c2f35dbefbcc" data-mmenu-position="position-left" data-mmenu-title="Menu" data-mmenu-stickytitle="on" data-mmenu-isslider="false" data-mmenu-closingicon="%7B%22desktop%22%3A%22off%22%2C%22tablet%22%3A%22off%22%2C%22mobile%22%3A%22off%22%7D" data-brz-custom-id="qZIFRiPnadng"><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" class="brz-menu brz-menu__preview brz-css-8iad6q brz-css-teu6jx"><ul class="brz-menu__ul"><li data-menu-item-id="0acdcd2167ae8ff12685ae786762acce" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt" title="Music Therapy"><span class="brz-span">Music Therapy</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="9e6100a7dfd3923b5aba72089e5e9ab0" class="brz-menu__item"><a class="brz-a" target="" href="/en/find" title="Find a music therapist"><span class="brz-span">Find a music therapist</span></a></li><li data-menu-item-id="910f2d2483a7024d1e2b58bc3c80ee0c" class="brz-menu__item"><a class="brz-a" target="" href="/en/study" title="Study music therapy"><span class="brz-span">Study music therapy</span></a></li><li data-menu-item-id="d0a5a0d016b7fc5c6a98a9160c4775c7" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt-info" title="For music therapists"><span class="brz-span">For music therapists</span></a></li></ul></li><li data-menu-item-id="bd98cfee5a455d72cddfcb74e20e8147" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt-blog" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="b272f2d5f5a48a173325c114ad596033" class="brz-menu__item"><a class="brz-a" target="" href="/en/contact" title="Contact us"><span class="brz-span">Contact us</span></a></li></ul></nav><div class="brz-mm-menu__icon"><svg class="brz-icon-svg align-[initial]"><use href="../../assets/svg/8e85539e41ed2686da0dcff2d601af13.svg#brz_icon"></use></svg></div><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" id="qZIFRiPnadng_id8899182c2f35dbefbcc" class="brz-menu brz-menu__preview brz-menu__mmenu brz-menu--has-dropdown brz-css-1t2dxpm brz-css-1en7lv1"><ul class="brz-menu__ul"><li data-menu-item-id="0acdcd2167ae8ff12685ae786762acce" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt" title="Music Therapy"><span class="brz-span">Music Therapy</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="9e6100a7dfd3923b5aba72089e5e9ab0" class="brz-menu__item"><a class="brz-a" target="" href="/en/find" title="Find a music therapist"><span class="brz-span">Find a music therapist</span></a></li><li data-menu-item-id="910f2d2483a7024d1e2b58bc3c80ee0c" class="brz-menu__item"><a class="brz-a" target="" href="/en/study" title="Study music therapy"><span class="brz-span">Study music therapy</span></a></li><li data-menu-item-id="d0a5a0d016b7fc5c6a98a9160c4775c7" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt-info" title="For music therapists"><span class="brz-span">For music therapists</span></a></li></ul></li><li data-menu-item-id="bd98cfee5a455d72cddfcb74e20e8147" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt-blog" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="b272f2d5f5a48a173325c114ad596033" class="brz-menu__item"><a class="brz-a" target="" href="/en/contact" title="Contact us"><span class="brz-span">Contact us</span></a></li></ul></nav></div></div><div id="" class="brz-css-1s9eguc brz-css-7jstn6 brz-wrapper"><div class="brz-menu__container brz-css-o30vek brz-css-xh7cfw" data-mmenu-id="#u25jnfbqlQlD_id8899182c2f35dbefbcc" data-mmenu-position="position-left" data-mmenu-title="" data-mmenu-stickytitle="on" data-mmenu-isslider="false" data-mmenu-closingicon="%7B%22desktop%22%3A%22off%22%2C%22tablet%22%3A%22off%22%2C%22mobile%22%3A%22off%22%7D" data-brz-custom-id="u25jnfbqlQlD"><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" class="brz-menu brz-menu__preview brz-css-8iad6q brz-css-13m1mto"><ul class="brz-menu__ul"><li data-menu-item-id="d28020b9b74d9f7d932124ee027dcb7e" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt" title="Music Therapy"><span class="brz-span">Music Therapy</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="5f375016778cc215f71e688860031be4" class="brz-menu__item"><a class="brz-a" target="" href="/en/find" title="Find a music therapist"><span class="brz-span">Find a music therapist</span></a></li><li data-menu-item-id="ab523f7bacafa54d54f414678b4fe489" class="brz-menu__item"><a class="brz-a" target="" href="/en/study" title="Study music therapy"><span class="brz-span">Study music therapy</span></a></li><li data-menu-item-id="cea700c7d9594ea816ea13ad96959670" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt-info" title="For music therapists"><span class="brz-span">For music therapists</span></a></li></ul></li><li data-menu-item-id="1733bfe0140cab641a552bcb0b21d10c" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt-blog" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="2e4992d888a6504e3edbb6f41442e5e7" class="brz-menu__item"><a class="brz-a" target="" href="/en/contact" title="Contact us"><span class="brz-span">Contact us</span></a></li><li data-menu-item-id="9927bdb365a5e8a6717243383fc0661d" class="brz-menu__item"><a class="brz-a" target="" href="/" title="EL"><span class="brz-span">EL</span></a></li></ul></nav><div class="brz-mm-menu__icon"><svg class="brz-icon-svg align-[initial]"><use href="../../assets/svg/8e85539e41ed2686da0dcff2d601af13.svg#brz_icon"></use></svg></div><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" id="u25jnfbqlQlD_id8899182c2f35dbefbcc" class="brz-menu brz-menu__preview brz-menu__mmenu brz-menu--has-dropdown brz-css-1t2dxpm brz-css-1pfv2km"><ul class="brz-menu__ul"><li data-menu-item-id="d28020b9b74d9f7d932124ee027dcb7e" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt" title="Music Therapy"><span class="brz-span">Music Therapy</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="5f375016778cc215f71e688860031be4" class="brz-menu__item"><a class="brz-a" target="" href="/en/find" title="Find a music therapist"><span class="brz-span">Find a music therapist</span></a></li><li data-menu-item-id="ab523f7bacafa54d54f414678b4fe489" class="brz-menu__item"><a class="brz-a" target="" href="/en/study" title="Study music therapy"><span class="brz-span">Study music therapy</span></a></li><li data-menu-item-id="cea700c7d9594ea816ea13ad96959670" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt-info" title="For music therapists"><span class="brz-span">For music therapists</span></a></li></ul></li><li data-menu-item-id="1733bfe0140cab641a552bcb0b21d10c" class="brz-menu__item"><a class="brz-a" target="" href="/en/mt-blog" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="2e4992d888a6504e3edbb6f41442e5e7" class="brz-menu__item"><a class="brz-a" target="" href="/en/contact" title="Contact us"><span class="brz-span">Contact us</span></a></li><li data-menu-item-id="9927bdb365a5e8a6717243383fc0661d" class="brz-menu__item"><a class="brz-a" target="" href="/" title="EL"><span class="brz-span">EL</span></a></li></ul></nav></div></div></div></div><div id="translatorclick" class="brz-columns brz-css-1au8x2i brz-css-1rk9xo9" data-brz-custom-id="tlxdTCj8gUrU"><div class="brz-column__items brz-css-bs698n brz-css-c6h42g"><div id="" class="brz-css-1s9eguc brz-css-1l081p9 brz-wrapper"><div class="brz-wrapper-transform"><div class="brz-icon-text brz-css-a1usu8 brz-css-1xajxap" data-brz-custom-id="mZd6xSmnfMPf"><div class="brz-icon__container" data-brz-custom-id="qX3vD2V99Fyz"><span class="brz-icon brz-span brz-css-8hiy7p brz-css-1jhn46a"><svg class="brz-icon-svg align-[initial]"><use href="../../assets/svg/673cf948d20838cde5e7c8f64474298f.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-17x399q brz-css-y38r88" data-brz-custom-id="ut37xCati9iY"><div data-brz-translate-text="1"><p class="brz-lh-lg-1_7 brz-ls-lg-m_0_2 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-16 brz-ft-google brz-ff-comfortaa brz-tp-lg-empty brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-text-lg-justify brz-mt-lg-0 brz-css-nU99y" data-generated-css="brz-css-bcGF6" data-uniq-id="ykmlM"><span class="brz-cp-color7" style="color: rgba(var(--brz-global-color7),1);">EL</span></p></div></div></div></div></div></div><div id="" class="brz-css-1s9eguc brz-css-j22a30 brz-wrapper"><div class="brz-embed-code brz-css-1nobro9 brz-css-r1fo27" data-brz-custom-id="glN9FfPQ_OkO"><div class="brz-embed-content"><div><style>#translatorclick {transition: transform .3s ease-in-out;}
#translatorclick:hover {transform: scale(1.08);}</style></div></div></div></div></div><a class="brz-a brz-container-link" href="/" target="_self" rel="noopener" data-brz-link-type="external"></a></div></div></header></div></div></section>     <section id="i3a021eaeb0ac1e007432_pbvayzybuemjdkmxlauverxdcbymotuexvjt" class="brz-section brz-css-310oex brz-css-1v4fvfm"><div class="brz-section__content brz-section--boxed brz-css-kt508o brz-css-faw0zd" data-brz-custom-id="pwdhdnsgdikvievbjwbwgpixjvnsetuokpgc"><div class="brz-bg"><div class="brz-bg-image"></div><div class="brz-bg-color"></div></div><div class="brz-container brz-css-w4i6p2 brz-css-1lbvlkv"><div id="" data-brz-iteration-count="1" class="brz-css-3qbalv brz-css-5dgfw4 brz-wrapper brz-animated brz-css-13d31ya brz-css-1csnxia" data-animationid="nrwifgynaqisxwexgcigbuejbcwezomwohti"><div class="brz-rich-text brz-rich-text__custom brz-css-1veoybo brz-css-1d9stus" data-brz-custom-id="ogcfqkkhpyotiqcssknlqdnvpgpmremubnuh"><div data-brz-translate-text="1"><h1 class="brz-text-xs-center brz-text-sm-left brz-bcp-color8 brz-fss-xs-px brz-fw-xs-700 brz-ls-xs-0 brz-lh-xs-1_3 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-fs-xs-32 brz-tp-lg-heading1 brz-text-lg-left brz-tp-xs-heading1 brz-css-bKPsZ" data-generated-css="brz-css-jKpw7" data-uniq-id="gEBIy"><span class="brz-cp-color8" style="text-shadow: rgb(84, 84, 84) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Music therapy in Cyprus</span></h1></div></div></div><div id="" data-brz-iteration-count="1" class="brz-css-3qbalv brz-css-15r3e4t brz-wrapper brz-animated brz-css-hwinur brz-css-cw7fqk" data-animationid="vQO3YRnvC8dr"><div class="brz-rich-text brz-rich-text__custom brz-css-1veoybo brz-css-gi2ck9" data-brz-custom-id="wFQTwHvI_MOn"><div data-brz-translate-text="1"><p class="brz-text-xs-center brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_4 brz-ls-lg-0 brz-fw-lg-100 brz-fss-lg-px brz-fs-lg-24 brz-ft-google brz-ff-overpass brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-fs-xs-20 brz-fss-xs-px brz-fw-xs-100 brz-ls-xs-0 brz-lh-xs-1_2 brz-tp-lg-subtitle brz-tp-xs-subtitle brz-css-eh_jO" data-uniq-id="oVNT0" data-generated-css="brz-css-ip8bB"><span class="brz-cp-color7" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgb(255, 255, 255);">Connect with specialized professionals</span></p></div></div></div></div></div></section><section id="id53d0f647bb13c309ec1_hplrazkanlqagkmqebsjuzpbkjgmidzwfsyf" class="brz-section brz-css-310oex brz-css-1oswvkr"><div class="brz-section__content brz-section--boxed brz-css-kt508o brz-css-1t4bujf" data-brz-custom-id="qpltnqdqzqsvnfeowvcbznmnxncqurlfvcnp"><div class="brz-container brz-css-w4i6p2 brz-css-1llvq5f"><div id="" class="brz-css-3qbalv brz-css-1atdaet brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1veoybo brz-css-1rb367g" data-brz-custom-id="giobevxsdkufoegkhtyrucgchknpuupqesfh"><div data-brz-translate-text="1"><h2 class="brz-tp-xs-heading2 brz-text-xs-center brz-tp-sm-heading2 brz-tp-lg-heading2 brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1 brz-ls-xs-0 brz-fw-xs-700 brz-fss-xs-px brz-fs-xs-32 brz-text-lg-left brz-fss-lg-px brz-fw-lg-700 brz-ls-lg-0 brz-lh-lg-1_2 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-ff-lato brz-ft-google brz-fs-lg-36 brz-fss-sm-px brz-fw-sm-700 brz-ls-sm-0 brz-lh-sm-1_2 brz-vfw-sm-400 brz-fwdth-sm-100 brz-fsft-sm-0 brz-fs-sm-32 brz-css-tn7NM" data-uniq-id="mQTlD" data-generated-css="brz-css-o70BO"><span class="brz-cp-color2">Find a Music Therapist</span></h2></div></div></div><div id="" class="brz-css-3qbalv brz-css-1y06q6o brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1veoybo brz-css-1h0e4l7" data-brz-custom-id="pzelfomixalozynxdrgeortqtypqzxyetzec"><div data-brz-translate-text="1"><p class="brz-tp-lg-paragraph brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-100 brz-fss-lg-px brz-fs-lg-18 brz-ft-google brz-ff-overpass brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_5 brz-ls-xs-0 brz-fw-xs-100 brz-fss-xs-px brz-fs-xs-18 brz-text-xs-center brz-text-lg-left brz-tp-xs-paragraph brz-css-bCdaM" data-uniq-id="aJ4pa" data-generated-css="brz-css-zlchG">Looking for a registered music therapist in Cyprus? We can help connect you with qualified professionals. Click below to explore our directory of certified practitioners.</p><p class="brz-tp-lg-paragraph brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-100 brz-fss-lg-px brz-fs-lg-18 brz-ft-google brz-ff-overpass brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_5 brz-ls-xs-0 brz-fw-xs-100 brz-fss-xs-px brz-fs-xs-18 brz-tp-xs-empty brz-text-xs-center brz-text-lg-left brz-css-wGEfh" data-uniq-id="cwZNO" data-generated-css="brz-css-zNIT9"><br></p><p class="brz-tp-lg-paragraph brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-100 brz-fss-lg-px brz-fs-lg-18 brz-ft-google brz-ff-overpass brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_5 brz-ls-xs-0 brz-fw-xs-100 brz-fss-xs-px brz-fs-xs-18 brz-text-xs-center brz-text-lg-left brz-tp-xs-paragraph brz-css-sTTUw" data-uniq-id="qf3Fk" data-generated-css="brz-css-b5nL_">Visit the relevant page of the Ministry of Health to view the updated registry of registered music therapists:</p></div></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1abvqnm brz-css-h4enic" data-brz-custom-id="i74tZTrc4mPS"><a class="brz-a brz-btn brz-css-1dzh008 brz-css-1emvac7 brz-css-1d74kyl brz-css-l5n4cz" href="https://www.gov.cy/moh/mitroo-eggegrammenon-moysikotherapeyton/" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="y0jzZBHuFgzc"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Music Therapist Registry</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1abvqnm brz-css-1wiyjm0" data-brz-custom-id="q0ZxogG_SxAC"><a class="brz-a brz-btn brz-css-1dzh008 brz-css-1qgxz9q brz-css-1d74kyl brz-css-1t73cfz" href="https://www.gov.cy/moh/mitroo-eggegrammenon-moysikotherapeyton/" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="wLibVnPXYbRO"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Μητρώο Εγγεγραμμένων<br>Μουσικοθεραπευτών</span></a></div><div id="" class="brz-css-3qbalv brz-css-jah8c5 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1veoybo brz-css-dq39l5" data-brz-custom-id="cVu9OSPZbGlp"><div data-brz-translate-text="1"><p class="brz-tp-lg-paragraph brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-100 brz-fss-lg-px brz-fs-lg-18 brz-ft-google brz-ff-overpass brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_5 brz-ls-xs-0 brz-fw-xs-100 brz-fss-xs-px brz-fs-xs-18 brz-text-xs-center brz-text-lg-left brz-tp-xs-paragraph brz-css-nr6C0" data-uniq-id="djpqT" data-generated-css="brz-css-qh6cN">If you need further help and guidance you can <a class="brz-cp-color2 link--external" style="color: rgba(var(--brz-global-color2),1);" href="/en/contact" data-brz-link-type="external"><strong><u>contact us</u></strong></a>.</p></div></div></div><div id="" class="brz-css-3qbalv brz-css-4ky6vy brz-css-yxekvk brz-wrapper"><div class="brz-spacer brz-css-1a6zrza brz-css-9dks9p"></div></div></div></div></section><section id="i8ec329e424daaa1301fa_h9bUKl9mwalI" class="brz-section brz-css-icza6t brz-css-1jzivbc"><div class="brz-section__content brz-section--boxed brz-css-112foev brz-css-phah2e" data-brz-custom-id="f0jbO4f8d2Fj"><div class="brz-bg"><div class="brz-bg-image"></div><div class="brz-bg-color"></div></div><div class="brz-container brz-css-1q2jeb8 brz-css-14dqn75"><div class="brz-row__container brz-css-2u69vb brz-css-tlsm9t" data-brz-custom-id="n0IImb592osT"><div class="brz-row brz-css-1aujj4f brz-css-1lmvhsw brz-css-swih1j"><div data-brz-iteration-count="1" class="brz-columns brz-css-1dg6w3w brz-css-5t5w8a brz-animated brz-css-fwup9k brz-css-12o7bs6" data-brz-custom-id="mGdRkCdlBcTD"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-column__items brz-css-1jiv5r7 brz-css-1cqgkiq"><div id="" class="brz-css-gviw7v brz-css-lowft brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-t4mrh8 brz-css-3dyjyl" data-brz-custom-id="nufn_u0yO2hY"><div data-brz-translate-text="1"><h2 class="brz-tp-xs-heading2 brz-text-lg-left brz-text-sm-left brz-tp-lg-heading2 brz-fs-xs-36 brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_2 brz-ls-xs-0 brz-fw-xs-700 brz-fss-xs-px brz-text-xs-center brz-bcp-color8 brz-fss-lg-px brz-fw-lg-700 brz-ls-lg-0 brz-lh-lg-1_2 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-ft-google brz-fs-lg-42 brz-ff-overpass brz-css-i4m2s" data-generated-css="brz-css-tK4ok" data-uniq-id="ilR8H"><span class="brz-cp-color8" style="text-shadow: rgb(84, 84, 84) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Who is Music Therapy For</span></h2></div></div></div><div id="" class="brz-css-gviw7v brz-css-sbh8w5 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-t4mrh8 brz-css-1jxds40" data-brz-custom-id="sSPNe4Ru9yU2"><div data-brz-translate-text="1"><p class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-css-xPMSF" data-generated-css="brz-css-hV08a" data-uniq-id="ksuKF"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Infants, preschoolers, school-age children, adolescents, adults, and the elderly can benefit from Music Therapy on an individual or group level.</span></p><p class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-css-sY6QH" data-generated-css="brz-css-bEVRw" data-uniq-id="hXCvw"><br></p><p class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-css-kdXZ5" data-generated-css="brz-css-zLC7E" data-uniq-id="boKfj"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Music Therapy is intended for individuals with:</span></p><p class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-css-vMsgd" data-generated-css="brz-css-eX0r0" data-uniq-id="urzEh"><br></p><ul><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-ecved" data-generated-css="brz-css-c1UaP" data-uniq-id="aJ3nc" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Mental disorders</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-aE5hC" data-generated-css="brz-css-srO10" data-uniq-id="w1_qN" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Anxiety and Stress</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-fwrXA" data-generated-css="brz-css-nFw0T" data-uniq-id="mm9s_" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Pervasive developmental disorders</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-bgff5" data-generated-css="brz-css-tTIpU" data-uniq-id="f__Bw" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Emotional and behavioral difficulties</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-zCLqE" data-generated-css="brz-css-vlaNq" data-uniq-id="sE_i3" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Learning difficulties</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-vsoXX" data-generated-css="brz-css-kvFEj" data-uniq-id="zQzwD" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Neurological difficulties</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-xevD6" data-generated-css="brz-css-fq34Q" data-uniq-id="kWepi" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Chronic illnesses/conditions</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-h2gx0" data-generated-css="brz-css-e5198" data-uniq-id="w0Hwc" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Intellectual disability</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-dvEBB" data-generated-css="brz-css-z70Dr" data-uniq-id="hF8e3" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Senile Dementia</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-bYNJc" data-generated-css="brz-css-r2BOX" data-uniq-id="iALK4" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Acquired disabilities</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-tPFgT" data-generated-css="brz-css-zOw05" data-uniq-id="boVYC" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Speech and language disorders</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-rSs_H" data-generated-css="brz-css-qJPlw" data-uniq-id="hJ7bz" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Eating disorders</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-rJGbq" data-generated-css="brz-css-lEl6w" data-uniq-id="wmdz_" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Addictions</span></li><li class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-bcp-color8 brz-css-pHs8Q" data-generated-css="brz-css-lzHYL" data-uniq-id="hnbPw" style="color: rgba(var(--brz-global-color8),1);"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Visual and auditory impairments</span></li></ul><p class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-css-kHGYz" data-generated-css="brz-css-pUtyK" data-uniq-id="lxKPJ"><br></p><p class="brz-ff-overpass brz-ft-google brz-fs-lg-18 brz-fss-lg-px brz-fw-lg-100 brz-ls-lg-0 brz-lh-lg-1_4 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-paragraph brz-css-icO37" data-generated-css="brz-css-wLOuN" data-uniq-id="fWclB"><span class="brz-cp-color8" style="text-shadow: rgb(5, 5, 5) 1px 2px 8px; color: rgba(var(--brz-global-color8),1);">Music Therapy is also intended for pregnant women and individuals seeking a means of expression, personal development, and support.</span></p></div></div></div></div></div></div></div></div></div></section>     <section id="footer" class="brz-section brz-css-kd0irf brz-css-qeaf84"><div class="brz-section__content brz-section--boxed brz-css-1u1eagc brz-css-1gdxim2" data-brz-custom-id="gHAuPp1zVNBC"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-container brz-css-cvhhtf brz-css-tm25nd"><div class="brz-row__container brz-css-2tdmu5 brz-css-u60gik" data-brz-custom-id="erjrq_VDfs7Q"><div class="brz-row brz-css-1cwzyu7 brz-css-72iks1 brz-css-iy5bpn"><div class="brz-columns brz-css-snmdvf brz-css-1rg80a8" data-brz-custom-id="sjihNGQO3yul"><div class="brz-column__items brz-css-nkqxln brz-css-1vtg2v0"><div id="" class="brz-css-9bmuwm brz-css-kuif0 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-hnubjp brz-css-1erwwuu" data-brz-custom-id="a3kwo1crqnGw"><div data-brz-translate-text="1"><p class="brz-tp-lg-subtitle brz-text-xs-justify brz-text-lg-right brz-ff-overpass brz-ft-google brz-fs-lg-20 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-0 brz-lh-lg-1_5 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-css-avgV7" data-uniq-id="gRqBA" data-generated-css="brz-css-g8XIj"><span class="brz-cp-color2">CyMTA</span></p></div></div></div><div id="" class="brz-css-9bmuwm brz-css-a44jyd brz-css-9hf8dj brz-wrapper"><div class="brz-spacer brz-css-1oatros brz-css-18vc5vh"></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1vvy1um brz-css-6yal2v" data-brz-custom-id="wdjATeeiaGFt"><a class="brz-a brz-btn brz-css-1cvvmts brz-css-a0tp39 brz-css-b5dipk brz-css-zgvzlb" href="/en/mt-blog" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="rb9HxtyjouNA"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Blog / Articles</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1vvy1um brz-css-z47noj" data-brz-custom-id="d8WGJEmqKctA"><a class="brz-a brz-btn brz-css-1cvvmts brz-css-ry3woq brz-css-b5dipk brz-css-r7my7g" href="/en/mt" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="mscI9rnDyow3"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Music Therapy</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1vvy1um brz-css-1oj9yue" data-brz-custom-id="kdXW2FXskTLg"><a class="brz-a brz-btn brz-css-1cvvmts brz-css-1ul4g6z brz-css-b5dipk brz-css-1pxnmd1" href="/en/find" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="xO_nlDtBLA2i"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Find a Music Therapist</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1vvy1um brz-css-1kfn6i7" data-brz-custom-id="coku3fugxEj9"><a class="brz-a brz-btn brz-css-1cvvmts brz-css-p2w8vq brz-css-b5dipk brz-css-486v1z" href="/en/study" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="sHnDf6_N9ho1"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Become a Music Therapist</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1vvy1um brz-css-1n6tvm3" data-brz-custom-id="bl3CVbnm6FBO"><a class="brz-a brz-btn brz-css-1cvvmts brz-css-3j3dtw brz-css-b5dipk brz-css-16hs2ni" href="/en/mt-info" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="w1L2WyOZ0vhf"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Useful Info for Music therapists</span></a></div></div></div><div class="brz-columns brz-css-snmdvf brz-css-ogcnkf" data-brz-custom-id="dW36SIimsfMH"><div class="brz-column__items brz-css-nkqxln brz-css-q4mwko"><div id="" class="brz-css-9bmuwm brz-css-2ev8yp brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-hnubjp brz-css-bslr0s" data-brz-custom-id="chzep6L_wDDU"><div data-brz-translate-text="1"><p class="brz-tp-lg-subtitle brz-text-xs-justify brz-ff-overpass brz-ft-google brz-fs-lg-20 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-0 brz-lh-lg-1_5 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-css-lwMzU" data-uniq-id="sTt3n" data-generated-css="brz-css-jDtkG"><span class="brz-cp-color2">Contact</span></p></div></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1vvy1um brz-css-1f8myrk" data-brz-custom-id="gOgtaXgNiEBS"><div class="brz-icon__container" data-brz-custom-id="oGPwd7jlipoN"><a class="brz-a" href="https://www.facebook.com/profile.php?id=61572078845247" target="_self" rel="noopener" data-brz-link-type="external"><span class="brz-icon brz-span brz-css-194nueb brz-css-1kgpxwi"><svg class="brz-icon-svg align-[initial]"><use href="../../assets/svg/d710c5ce3ce8671b1521487d79654e3d.svg#nc_icon"></use></svg></span></a></div><div class="brz-icon__container" data-brz-custom-id="v0zInfDjatEO"><a class="brz-a" href="https://www.instagram.com/music_therapy_cy" target="_self" rel="noopener" data-brz-link-type="external"><span class="brz-icon brz-span brz-css-194nueb brz-css-32ywyv"><svg class="brz-icon-svg align-[initial]"><use href="../../assets/svg/207d5e57bd91d810599bb7b29b33c358.svg#nc_icon"></use></svg></span></a></div></div><div id="" class="brz-css-9bmuwm brz-css-a44jyd brz-css-bj21vk brz-wrapper"><div class="brz-spacer brz-css-1oatros brz-css-1s6iklb"></div></div><div id="" class="brz-css-9bmuwm brz-css-zn2o2 brz-wrapper"><div class="brz-icon-text brz-css-nqhvk6 brz-css-fp6t55" data-brz-custom-id="vATQi6yPPepB"><div class="brz-icon__container" data-brz-custom-id="zaWVpEkREAGU"><span class="brz-icon brz-span brz-css-194nueb brz-css-1avk5dr"><svg class="brz-icon-svg align-[initial]"><use href="../../assets/svg/3ab13401b5087e89fe0e4d04b6a9bcd4.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-hnubjp brz-css-16t8pfx" data-brz-custom-id="k2pg88L4Hb56"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-16 brz-ft-google brz-ff-overpass brz-fs-xs-14 brz-fss-xs-px brz-fw-xs-400 brz-ls-xs-0 brz-lh-xs-1_6 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-text-xs-justify brz-tp-lg-uGuDLCdCXLbq brz-tp-xs-uGuDLCdCXLbq brz-css-sGE8L" data-generated-css="brz-css-cvCqH" data-uniq-id="vhy6J"><a class="link--external brz-cp-color7" href="tel:+35797661501" data-brz-link-type="external">+357 97 661501</a></p></div></div></div></div></div><div id="" class="brz-css-9bmuwm brz-css-tb1f9i brz-wrapper"><div class="brz-icon-text brz-css-nqhvk6 brz-css-yudjf0" data-brz-custom-id="cYtCwSOC1_t_"><div class="brz-icon__container" data-brz-custom-id="dBPTKYS_fymo"><span class="brz-icon brz-span brz-css-194nueb brz-css-1eifw7k"><svg class="brz-icon-svg align-[initial]"><use href="../../assets/svg/3eae82f17391f20cdfc33c0710557915.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-hnubjp brz-css-1qbv217" data-brz-custom-id="uvCjtttrAVaW"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-16 brz-ft-google brz-ff-overpass brz-fs-xs-14 brz-fss-xs-px brz-fw-xs-400 brz-ls-xs-0 brz-lh-xs-1_6 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-text-xs-justify brz-tp-lg-uGuDLCdCXLbq brz-tp-xs-uGuDLCdCXLbq brz-css-vydA7" data-generated-css="brz-css-nKI4g" data-uniq-id="pLGcD"><a class="link--external brz-cp-color7" href="mailto: <EMAIL> " data-brz-link-type="external" target="_blank"><EMAIL> </a></p></div></div></div></div></div><div id="" class="brz-css-9bmuwm brz-css-9plwpn brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-hnubjp brz-css-17dn06g" data-brz-custom-id="jk_LGStSUPqk"><div data-brz-translate-text="1"><p class="brz-tp-xs-uGuDLCdCXLbq brz-tp-lg-uGuDLCdCXLbq brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_8 brz-ls-xs-0 brz-fw-xs-400 brz-fss-xs-px brz-fs-xs-14 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-0 brz-lh-lg-2 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-ft-google brz-fs-lg-16 brz-ff-overpass brz-text-xs-left brz-css-siudR" data-uniq-id="fXT1V" data-generated-css="brz-css-aE7oZ"><span class="brz-cp-color7">Οινόης 7, Στρόβολος 2037 Λευκωσία</span></p></div></div></div></div></div></div></div><div id="" class="brz-css-9bmuwm brz-css-a44jyd brz-css-uqyua6 brz-wrapper"><div class="brz-spacer brz-css-1oatros brz-css-hpna1y"></div></div><div id="" class="brz-css-9bmuwm brz-css-14ulx76 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-hnubjp brz-css-8j5v47" data-brz-custom-id="nmA5ELrQho54"><div data-brz-translate-text="1"><p class="brz-tp-lg-uGuDLCdCXLbq brz-text-lg-center brz-ff-overpass brz-ft-google brz-fs-lg-16 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-0 brz-lh-lg-1_6 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-xs-u9Xihr8QXhSs brz-css-zC7Kb" data-uniq-id="jJY6l" data-generated-css="brz-css-eBk08"><span class="brz-cp-color7">© 2025 Cyprus Music Therapy Association. </span></p></div></div></div></div></div></section>     </div>          </body></html>