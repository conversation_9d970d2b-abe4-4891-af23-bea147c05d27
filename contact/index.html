<!DOCTYPE html><html lang="en"><head><meta charset="utf-8" /><meta property="og:site_name" content="" /><meta property="og:type" content="article" /><meta property="og:url" content="/contact" /><meta content="summary_large_image" name="twitter:card" />          <meta property="og:title" content="Επικοινωνία" />     <meta name="twitter:title" content="Επικοινωνία" />      <meta property="og:description" content="Έχετε απορίες ή ερωτήσεις σχετικά με τη μουσικοθεραπεία; Επικοινωνήστε μαζί μας!" />     <meta name="twitter:description" content="Έχετε απορίες ή ερωτήσεις σχετικά με τη μουσικοθεραπεία; Επικοινωνήστε μαζί μας!" />      <meta property="og:image" content="../assets/f28a8433c4afdfbd232cf5503309b4cb.webp" />     <meta property="twitter:image" content="../assets/f28a8433c4afdfbd232cf5503309b4cb.webp">      <title>Επικοινωνία</title>      <meta name="description" content="Έχετε απορίες ή ερωτήσεις σχετικά με τη μουσικοθεραπεία; Επικοινωνήστε μαζί μας !" />            <link rel="canonical" href="https://www.cymta.org/contact" /><link rel="alternate" hreflang="el" href="https://www.cymta.org/contact" /><link rel="alternate" hreflang="en" href="https://www.cymta.org/en/contact" /><link rel="icon" href="https://cloud-1de12d.b-cdn.net/media/iW=32%26iH=any/5800e034b05aa57cedaa6e50ab5154b2.png" sizes="32x32"/><link rel="icon" href="https://cloud-1de12d.b-cdn.net/media/iW=192%26iH=any/5800e034b05aa57cedaa6e50ab5154b2.png" sizes="192x192"/><link rel="apple-touch-icon-precomposed" href="https://cloud-1de12d.b-cdn.net/media/iW=180&iH=any/5800e034b05aa57cedaa6e50ab5154b2.png"/><meta name="viewport" content="width=device-width, initial-scale=1"><link class="brz-link brz-link-bunny-fonts-prefetch" rel="dns-prefetch" href="//fonts.bunny.net"><link class="brz-link brz-link-bunny-fonts-preconnect" rel="preconnect" href="https://fonts.bunny.net/" crossorigin><link class="brz-link brz-link-cdn-preconnect" rel="preconnect" href="https://cloud-1de12d.b-cdn.net" crossorigin><link href="https://fonts.bunny.net/css?family=Comfortaa:300,regular,500,600,700|Lato:100,100italic,300,300italic,regular,italic,700,700italic,900,900italic&subset=arabic,bengali,cyrillic,cyrillic-ext,devanagari,greek,greek-ext,gujarati,hebrew,khmer,korean,latin-ext,tamil,telugu,thai,vietnamese&display=swap" class="brz-link brz-link-google" type="text/css" rel="stylesheet"/><link href="../assets/2fe1a847ecb30e90fd3618351e71b7b0.css" class="brz-link brz-link-preview-lib" data-brz-group="group-1" rel="stylesheet"/><link href="../assets/07575d7e3674e1a8b8d8a30025ca06cb.css" class="brz-link brz-link-preview-lib-pro" data-brz-group="group-1_2" rel="stylesheet"/><link href="../assets/a1c351b066e704f38f6ab6530f000598.css" class="brz-link brz-link-preview-pro" rel="stylesheet"/><style class="brz-style">.brz .brz-css-1tbl67r {z-index: auto;margin: 0;}
.brz .brz-css-1tbl67r.brz-section .brz-section__content {min-height: auto;display: flex;}
.brz .brz-css-1tbl67r .brz-container {justify-content: center;}
.brz .brz-css-1tbl67r > .slick-slider > .brz-slick-slider__dots {color: rgba(0,0,0,1);}
.brz .brz-css-1tbl67r > .slick-slider > .brz-slick-slider__arrow {color: rgba(0,0,0,.7);}
@media (min-width:991px) {.brz .brz-css-1tbl67r {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tbl67r {display: block;}}
@media (max-width:767px) {.brz .brz-css-1tbl67r {display: block;}}
.brz .brz-css-ks7vcw {margin: -100px 0px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-ks7vcw {z-index: auto;margin: -100px 0px 0px 0px;}
	.brz .brz-css-ks7vcw.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-ks7vcw .brz-container {justify-content: center;}
	.brz .brz-css-ks7vcw > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-ks7vcw > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-ks7vcw:hover {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-ks7vcw {margin: -14% 0px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-ks7vcw {margin: -22% 0px 0px 0px;}}
.brz .brz-css-1ibzn60 {padding: 75px 0px 75px 0px;}
.brz .brz-css-1ibzn60 > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-1ibzn60 > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}
@media (min-width:991px) {.brz .brz-css-1ibzn60 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1ibzn60 {padding: 50px 15px 50px 15px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1ibzn60 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1ibzn60 {padding: 25px 15px 25px 15px;}}
@media (max-width:767px) {.brz .brz-css-1ibzn60 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ibzn60 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-6g79fi {padding: 288px 48px 256px 48px;}
.brz .brz-css-6g79fi > .brz-bg > .brz-bg-image {background-image: url("../assets/09605dba8d8b8a2c1f5491e128be4306.webp");background-position: 51% 100%;}
.brz .brz-css-6g79fi > .brz-bg > .brz-bg-image:after {content: "";background-image: url("../assets/09605dba8d8b8a2c1f5491e128be4306.webp");}
.brz .brz-css-6g79fi > .brz-bg > .brz-bg-color {background-color: transparent;background-image: linear-gradient(25deg,rgba(var(--brz-global-color3),.47) 0%,rgba(255,255,255,.88) 100%);}
@media (min-width:991px) {.brz .brz-css-6g79fi > .brz-bg > .brz-bg-image {background-attachment: fixed;}}
@media (min-width:991px) {.brz .brz-css-6g79fi {padding: 288px 48px 256px 48px;}
	.brz .brz-css-6g79fi > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-6g79fi:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-image {background-image: url("../assets/09605dba8d8b8a2c1f5491e128be4306.webp");filter: none;background-position: 51% 100%;display: block;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: url("../assets/09605dba8d8b8a2c1f5491e128be4306.webp");}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-color {background-color: transparent;background-image: linear-gradient(25deg,rgba(var(--brz-global-color3),.47) 0%,rgba(255,255,255,.88) 100%);}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-6g79fi > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-6g79fi:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-image {background-attachment: fixed;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-6g79fi:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-6g79fi {padding: 259px 15px 146px 15px;}}
@media (max-width:767px) {.brz .brz-css-6g79fi {padding: 203px 15px 133px 15px;}}
.brz .brz-css-1eg5ds5 {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1eg5ds5 {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1eg5ds5 {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-1eg5ds5 {max-width: 100%;}}
@media (min-width:991px) {.brz .brz-css-54tm0n {max-width: calc(.9 * var(--brz-section-container-max-width,1170px));}}
@media (min-width:991px) {.brz .brz-css-54tm0n:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-54tm0n {max-width: calc(.9 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-54tm0n {max-width: 75%;}}
@media (max-width:767px) {.brz .brz-css-54tm0n {max-width: 90%;}}
.brz .brz-css-1gxjaun {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-1gxjaun .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-1gxjaun {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1gxjaun {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1gxjaun {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-1gh8ats {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
	.brz .brz-css-1gh8ats .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1gh8ats {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-lxk38o {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-pasmh5 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-zfjee {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading1fontfamily,initial) !important;font-size: var(--brz-heading1fontsize,initial);line-height: var(--brz-heading1lineheight,initial);font-weight: var(--brz-heading1fontweight,initial);font-weight: var(--brz-heading1bold,initial);letter-spacing: var(--brz-heading1letterspacing,initial);font-variation-settings: var(--brz-heading1fontvariation,initial);font-style: var(--brz-heading1italic,initial);text-decoration: var(--brz-heading1textdecoration,initial) !important;text-transform: var(--brz-heading1texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-zfjee {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading1fontfamily,initial) !important;font-size: var(--brz-heading1fontsize,initial);line-height: var(--brz-heading1lineheight,initial);font-weight: var(--brz-heading1fontweight,initial);font-weight: var(--brz-heading1bold,initial);letter-spacing: var(--brz-heading1letterspacing,initial);font-variation-settings: var(--brz-heading1fontvariation,initial);font-style: var(--brz-heading1italic,initial);text-decoration: var(--brz-heading1textdecoration,initial) !important;text-transform: var(--brz-heading1texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-zfjee {font-size: var(--brz-heading1tabletfontsize,initial);line-height: var(--brz-heading1tabletlineheight,initial);font-weight: var(--brz-heading1tabletfontweight,initial);font-weight: var(--brz-heading1tabletbold,initial);letter-spacing: var(--brz-heading1tabletletterspacing,initial);font-variation-settings: var(--brz-heading1tabletfontvariation,initial);font-style: var(--brz-heading1tabletitalic,initial);text-decoration: var(--brz-heading1tablettextdecoration,initial) !important;text-transform: var(--brz-heading1tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-zfjee {font-size: var(--brz-heading1mobilefontsize,initial);line-height: var(--brz-heading1mobilelineheight,initial);font-weight: var(--brz-heading1mobilefontweight,initial);font-weight: var(--brz-heading1mobilebold,initial);letter-spacing: var(--brz-heading1mobileletterspacing,initial);font-variation-settings: var(--brz-heading1mobilefontvariation,initial);font-style: var(--brz-heading1mobileitalic,initial);text-decoration: var(--brz-heading1mobiletextdecoration,initial) !important;text-transform: var(--brz-heading1mobiletexttransform,initial) !important;}}
.brz .brz-css-1tk619v {margin: 10px 46% 10px 0px;}
@media (min-width:991px) {.brz .brz-css-1tk619v {padding: 0;margin: 10px 46% 10px 0px;justify-content: center;position: relative;}
	.brz .brz-css-1tk619v .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1tk619v {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tk619v {margin: 10px 28% 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-1tk619v {margin: 10px 0px 10px 0px;}}
@media (min-width:991px) {.brz .brz-css-1y7w3xf {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-m3l_8 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-m3l_8 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-m3l_8 {font-size: var(--brz-subtitletabletfontsize,initial);line-height: var(--brz-subtitletabletlineheight,initial);font-weight: var(--brz-subtitletabletfontweight,initial);font-weight: var(--brz-subtitletabletbold,initial);letter-spacing: var(--brz-subtitletabletletterspacing,initial);font-variation-settings: var(--brz-subtitletabletfontvariation,initial);font-style: var(--brz-subtitletabletitalic,initial);text-decoration: var(--brz-subtitletablettextdecoration,initial) !important;text-transform: var(--brz-subtitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-m3l_8 {font-size: var(--brz-subtitlemobilefontsize,initial);line-height: var(--brz-subtitlemobilelineheight,initial);font-weight: var(--brz-subtitlemobilefontweight,initial);font-weight: var(--brz-subtitlemobilebold,initial);letter-spacing: var(--brz-subtitlemobileletterspacing,initial);font-variation-settings: var(--brz-subtitlemobilefontvariation,initial);font-style: var(--brz-subtitlemobileitalic,initial);text-decoration: var(--brz-subtitlemobiletextdecoration,initial) !important;text-transform: var(--brz-subtitlemobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1scz54i {z-index: auto;margin: 0;}
	.brz .brz-css-1scz54i.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-1scz54i .brz-container {justify-content: center;}
	.brz .brz-css-1scz54i > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-1scz54i > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-1scz54i:hover {display: block;}}
.brz .brz-css-10twr81 {padding: 0px 48px 88px 48px;}
.brz .brz-css-10twr81 > .brz-bg > .brz-bg-image {background-image: url("../assets/09605dba8d8b8a2c1f5491e128be4306.webp");background-position: 50% 100%;}
.brz .brz-css-10twr81 > .brz-bg > .brz-bg-image:after {content: "";background-image: url("../assets/09605dba8d8b8a2c1f5491e128be4306.webp");}
.brz .brz-css-10twr81 > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color6),.9);}
@media (min-width:991px) {.brz .brz-css-10twr81 > .brz-bg > .brz-bg-image {background-attachment: fixed;}}
@media (min-width:991px) {.brz .brz-css-10twr81 {padding: 0px 48px 88px 48px;}
	.brz .brz-css-10twr81 > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-10twr81:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-image {background-image: url("../assets/09605dba8d8b8a2c1f5491e128be4306.webp");filter: none;background-position: 50% 100%;display: block;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: url("../assets/09605dba8d8b8a2c1f5491e128be4306.webp");}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color6),.9);background-image: none;}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-10twr81 > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-10twr81:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-image {background-attachment: fixed;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10twr81:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-10twr81 {padding: 48px 40px 75px 40px;}}
@media (max-width:767px) {.brz .brz-css-10twr81 {padding: 0px 30px 44px 30px;}}
@media (min-width:991px) {.brz .brz-css-koybsp:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-koybsp {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-1veody8 {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-1veody8 > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-1veody8 > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1veody8 > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-1veody8 > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1veody8 {min-height: auto;display: flex;}
	.brz .brz-css-1veody8 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1veody8 {min-height: auto;display: flex;}
	.brz .brz-css-1veody8 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-1veody8 {min-height: auto;display: flex;}
	.brz .brz-css-1veody8 > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1veody8 > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (min-width:991px) {.brz .brz-css-1th7qlv {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-1th7qlv > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-1th7qlv:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1th7qlv > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1th7qlv:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1th7qlv:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1th7qlv > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1th7qlv:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1th7qlv > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1th7qlv:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1th7qlv > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1th7qlv:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1th7qlv:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1th7qlv {min-height: auto;display: flex;}
	.brz .brz-css-1th7qlv:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1th7qlv:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1th7qlv:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1th7qlv:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1th7qlv:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1f0zir6 {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1f0zir6 {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1f0zir6 {padding: 0;}}
.brz .brz-css-181mqo8 {padding: 0;}
@media (min-width:991px) {.brz .brz-css-tz6ee8 {padding: 0;max-width: 100%;}}
.brz .brz-css-ui298x {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-ui298x .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-ui298x > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-ui298x > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-ui298x > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-ui298x > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ui298x > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ui298x > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-ui298x > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ui298x > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ui298x > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-ui298x {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-ui298x > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-ui298x > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ui298x > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ui298x > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1luw88h {flex: 1 1 63%;max-width: 63%;}
.brz .brz-css-1luw88h > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1luw88h {z-index: auto;flex: 1 1 63%;max-width: 63%;justify-content: flex-start;}
	.brz .brz-css-1luw88h .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-1luw88h > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-1luw88h:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1luw88h > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1luw88h:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1luw88h:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1luw88h > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1luw88h:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1luw88h > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1luw88h:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1luw88h > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1luw88h:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1luw88h:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1luw88h:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1luw88h:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1luw88h {flex: 1 1 100%;max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-1luw88h {flex: 1 1 100%;max-width: 100%;}}
.brz .brz-css-cbzve2 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-cbzve2 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-cbzve2 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-cbzve2 {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-cbzve2 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1pc7pji {margin: 0;padding: 0px 12% 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1pc7pji {z-index: auto;margin: 0;border: 0px solid transparent;padding: 0px 12% 0px 0px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1pc7pji:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1pc7pji {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1pc7pji {padding: 48px 0px 0px 0px;}}
.brz .brz-css-ms6n28 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1ohaa5w {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1ohaa5w .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1ohaa5w {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1ohaa5w {display: none;}}
@media (max-width:767px) {.brz .brz-css-1ohaa5w {display: none;}}
.brz .brz-css-1aho7cw {height: 50px;}
.brz .brz-css-ga6b1k {height: 120px;}
@media (min-width:991px) {.brz .brz-css-ga6b1k {height: 120px;}}
.brz .brz-css-1o759s8 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1o759s8 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1o759s8 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1o759s8 {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-1msabsm {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-okatz {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading2fontfamily,initial) !important;font-size: var(--brz-heading2fontsize,initial);line-height: var(--brz-heading2lineheight,initial);font-weight: var(--brz-heading2fontweight,initial);font-weight: var(--brz-heading2bold,initial);letter-spacing: var(--brz-heading2letterspacing,initial);font-variation-settings: var(--brz-heading2fontvariation,initial);font-style: var(--brz-heading2italic,initial);text-decoration: var(--brz-heading2textdecoration,initial) !important;text-transform: var(--brz-heading2texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-okatz {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading2fontfamily,initial) !important;font-size: var(--brz-heading2fontsize,initial);line-height: var(--brz-heading2lineheight,initial);font-weight: var(--brz-heading2fontweight,initial);font-weight: var(--brz-heading2bold,initial);letter-spacing: var(--brz-heading2letterspacing,initial);font-variation-settings: var(--brz-heading2fontvariation,initial);font-style: var(--brz-heading2italic,initial);text-decoration: var(--brz-heading2textdecoration,initial) !important;text-transform: var(--brz-heading2texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-okatz {font-size: var(--brz-heading2tabletfontsize,initial);line-height: var(--brz-heading2tabletlineheight,initial);font-weight: var(--brz-heading2tabletfontweight,initial);font-weight: var(--brz-heading2tabletbold,initial);letter-spacing: var(--brz-heading2tabletletterspacing,initial);font-variation-settings: var(--brz-heading2tabletfontvariation,initial);font-style: var(--brz-heading2tabletitalic,initial);text-decoration: var(--brz-heading2tablettextdecoration,initial) !important;text-transform: var(--brz-heading2tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-okatz {font-size: var(--brz-heading2mobilefontsize,initial);line-height: var(--brz-heading2mobilelineheight,initial);font-weight: var(--brz-heading2mobilefontweight,initial);font-weight: var(--brz-heading2mobilebold,initial);letter-spacing: var(--brz-heading2mobileletterspacing,initial);font-variation-settings: var(--brz-heading2mobilefontvariation,initial);font-style: var(--brz-heading2mobileitalic,initial);text-decoration: var(--brz-heading2mobiletextdecoration,initial) !important;text-transform: var(--brz-heading2mobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-vvbv0v {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-vvbv0v .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-vvbv0v {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-xcrd1l {height: 41px;}
@media (min-width:991px) {.brz .brz-css-xcrd1l {height: 41px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-xcrd1l {height: 35px;}}
@media (max-width:767px) {.brz .brz-css-xcrd1l {height: 25px;}}
.brz .brz-css-4yi92m {margin: 0;}
@media (min-width:991px) {.brz .brz-css-4yi92m {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-4yi92m .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-4yi92m {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-1wvksj6 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-eVSNG {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-eVSNG {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-paragraphfontfamily,initial) !important;font-size: var(--brz-paragraphfontsize,initial);line-height: var(--brz-paragraphlineheight,initial);font-weight: var(--brz-paragraphfontweight,initial);font-weight: var(--brz-paragraphbold,initial);letter-spacing: var(--brz-paragraphletterspacing,initial);font-variation-settings: var(--brz-paragraphfontvariation,initial);font-style: var(--brz-paragraphitalic,initial);text-decoration: var(--brz-paragraphtextdecoration,initial) !important;text-transform: var(--brz-paragraphtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-eVSNG {font-size: var(--brz-paragraphtabletfontsize,initial);line-height: var(--brz-paragraphtabletlineheight,initial);font-weight: var(--brz-paragraphtabletfontweight,initial);font-weight: var(--brz-paragraphtabletbold,initial);letter-spacing: var(--brz-paragraphtabletletterspacing,initial);font-variation-settings: var(--brz-paragraphtabletfontvariation,initial);font-style: var(--brz-paragraphtabletitalic,initial);text-decoration: var(--brz-paragraphtablettextdecoration,initial) !important;text-transform: var(--brz-paragraphtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-eVSNG {font-size: var(--brz-paragraphmobilefontsize,initial);line-height: var(--brz-paragraphmobilelineheight,initial);font-weight: var(--brz-paragraphmobilefontweight,initial);font-weight: var(--brz-paragraphmobilebold,initial);letter-spacing: var(--brz-paragraphmobileletterspacing,initial);font-variation-settings: var(--brz-paragraphmobilefontvariation,initial);font-style: var(--brz-paragraphmobileitalic,initial);text-decoration: var(--brz-paragraphmobiletextdecoration,initial) !important;text-transform: var(--brz-paragraphmobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-na1cz1 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-na1cz1 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-na1cz1 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-nqqubv {height: 50px;}
@media (min-width:991px) {.brz .brz-css-nqqubv {height: 50px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-nqqubv {height: 35px;}}
@media (max-width:767px) {.brz .brz-css-nqqubv {height: 25px;}}
.brz .brz-css-kh4pcm {margin: 0;}
@media (min-width:991px) {.brz .brz-css-kh4pcm {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-kh4pcm .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-kh4pcm {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-j299c5 .brz-form {margin: 0 -7.5px -15px -7.5px;}
.brz .brz-css-j299c5 .brz-forms2__item, .brz .brz-css-j299c5 .brz-form-ms-buttons {padding: 0 7.5px 15px 7.5px;}
.brz .brz-css-j299c5 .brz-forms2__item-button {margin-inline-end: auto;margin-inline-start: 0;max-width: 100%;flex-basis: 100%;}
.brz .brz-css-j299c5 .brz-forms2-story .brz-btn:before, .brz .brz-css-j299c5 .brz-form-ms-buttons--story .brz-btn:before {content: "";padding-top: 15%;}
.brz .brz-css-j299c5 .brz-form-ms-indicators {margin: 0px 7.5px 15px 7.5px;}
.brz .brz-css-1gagtz2 .brz-forms2__item-button {margin-inline-end: auto;margin-inline-start: 0;max-width: 50%;flex-basis: 50%;}
@media (min-width:991px) {.brz .brz-css-1gagtz2 .brz-form {margin: 0 -7.5px -15px -7.5px;}
	.brz .brz-css-1gagtz2 .brz-forms2__item, .brz .brz-css-1gagtz2 .brz-form-ms-buttons {padding: 0 7.5px 15px 7.5px;}
	.brz .brz-css-1gagtz2 .brz-forms2__item-button {margin-inline-end: auto;margin-inline-start: 0;max-width: 50%;flex-basis: 50%;}
	.brz .brz-css-1gagtz2 .brz-forms2-story .brz-btn:before, .brz .brz-css-1gagtz2 .brz-form-ms-buttons--story .brz-btn:before {content: "";padding-top: 15%;}
	.brz .brz-css-1gagtz2 .brz-form-ms-indicators {margin: 0px 7.5px 15px 7.5px;}}
@media (max-width:767px) {.brz .brz-css-1gagtz2 .brz-forms2__item-button {margin-inline-start: auto;margin-inline-end: auto;max-width: 100%;flex-basis: 100%;}}
.brz .brz-css-1g9uile {font-family: "Lato",sans-serif;font-size: 18px;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;}
.brz .brz-css-1g9uile {color: rgba(115,119,127,.7);}
.brz .brz-css-1g9uile ~ .brz-forms2__alert {font-family: "Lato",sans-serif;}
.brz .brz-css-1g9uile .brz-forms2__field {text-transform: inherit !important;}
.brz .brz-css-1g9uile .brz-forms2__field-label {color: rgba(115,119,127,.7);}
.brz .brz-css-1g9uile .brz-forms2__field-label {padding: 0px 0px 5px 0px;font-family: "Lato",sans-serif;font-size: 16px;font-weight: 400;letter-spacing: 0px;text-align: start;line-height: 1.5;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}
.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {border-radius: 0px;min-height: 57px;}
.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {color: rgba(115,119,127,.7);background-color: rgba(255,255,255,1);border: 1px solid rgba(220,222,225,1);box-shadow: none;}
.brz .brz-css-1g9uile.brz-forms2__item--error .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {border-color: #f00;}
.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox):not(.brz-forms2__field-select) {padding: 14px 24px;}
.brz .brz-css-1g9uile .brz-forms2__field-paragraph {line-height: 1.5;}
.brz .brz-css-1g9uile .brz-forms2__radio, .brz .brz-css-1g9uile .brz-forms2__checkbox {font-family: "Lato",sans-serif;font-size: 16px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}
.brz .brz-css-1g9uile .brz-forms2__radio, .brz .brz-css-1g9uile .brz-forms2__checkbox {color: rgba(115,119,127,.7);}
.brz .brz-css-1g9uile .brz-forms2__checkbox-option-name, .brz-forms2__radio-option-name {text-transform: inherit !important;}
.brz .brz-css-1g9uile .brz-forms2__select-item__input {color: rgba(115,119,127,.7);}
.brz .brz-css-1g9uile .form-alert {font-family: "Lato",sans-serif;}
@media (min-width:991px) {.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {transition-duration: .5s;}
	.brz .brz-css-1g9uile .brz-forms2__radio, .brz .brz-css-1g9uile .brz-forms2__checkbox {transition-duration: .5s;}
	.brz .brz-css-1g9uile .brz-forms2__select-item__input {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1g9uile {font-size: 14px;}
	.brz .brz-css-1g9uile .brz-forms2__field-label {font-size: 14px;}
	.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {min-height: 51px;}
	.brz .brz-css-1g9uile .brz-forms2__radio, .brz .brz-css-1g9uile .brz-forms2__checkbox {font-size: 14px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {transition-duration: .5s;}
	.brz .brz-css-1g9uile .brz-forms2__radio, .brz .brz-css-1g9uile .brz-forms2__checkbox {transition-duration: .5s;}
	.brz .brz-css-1g9uile .brz-forms2__select-item__input {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-1g9uile {font-size: 14px;}
	.brz .brz-css-1g9uile .brz-forms2__field-label {font-size: 14px;}
	.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {min-height: 43px;}
	.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox):not(.brz-forms2__field-select) {padding: 10px 20px;}
	.brz .brz-css-1g9uile .brz-forms2__radio, .brz .brz-css-1g9uile .brz-forms2__checkbox {font-size: 14px;}}
@media (max-width:767px) {.brz .brz-css-1g9uile .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {transition-duration: .5s;}
	.brz .brz-css-1g9uile .brz-forms2__radio, .brz .brz-css-1g9uile .brz-forms2__checkbox {transition-duration: .5s;}
	.brz .brz-css-1g9uile .brz-forms2__select-item__input {transition-duration: .5s;}}
.brz .brz-css-du33fw {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);}
.brz .brz-css-du33fw {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-du33fw ~ .brz-forms2__alert {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);}
.brz .brz-css-du33fw .brz-forms2__field {font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
.brz .brz-css-du33fw .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {min-height: NaNpx;}
.brz .brz-css-du33fw .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {color: rgba(var(--brz-global-color7),1);background-color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(220,222,225,0);}
.brz .brz-css-du33fw.brz-forms2__item--error .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {border: 2px solid #f00;}
.brz .brz-css-du33fw .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox):not(.brz-forms2__field-select) {padding: 20px 30px;}
.brz .brz-css-du33fw .brz-forms2__field-paragraph {line-height: var(--brz-ugudlcdcxlbqlineheight,initial);}
.brz .brz-css-du33fw .brz-forms2__select-item__input {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-du33fw .form-alert {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);}
@media (min-width:991px) {.brz .brz-css-du33fw {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);}
	.brz .brz-css-du33fw:hover {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-du33fw ~ .brz-forms2__alert {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);}
	.brz .brz-css-du33fw .brz-forms2__field {font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
	.brz .brz-css-du33fw:hover .brz-forms2__field-label {color: rgba(115,119,127,.7);}
	.brz .brz-css-du33fw .brz-forms2__field-label {padding: 0px 0px 5px 0px;font-family: "Lato",sans-serif;font-size: 16px;font-weight: 400;letter-spacing: 0px;text-align: start;line-height: 1.5;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}
	.brz .brz-css-du33fw .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {border-radius: 0px;min-height: NaNpx;}
	.brz .brz-css-du33fw:hover .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {color: rgba(var(--brz-global-color7),1);background-color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(220,222,225,0);box-shadow: none;}
	.brz .brz-css-du33fw.brz-forms2__item--error .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {border: 2px solid #f00;}
	.brz .brz-css-du33fw .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox):not(.brz-forms2__field-select) {padding: 20px 30px;}
	.brz .brz-css-du33fw .brz-forms2__field-paragraph {line-height: var(--brz-ugudlcdcxlbqlineheight,initial);}
	.brz .brz-css-du33fw .brz-forms2__radio, .brz .brz-css-du33fw .brz-forms2__checkbox {font-family: "Lato",sans-serif;font-size: 16px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}
	.brz .brz-css-du33fw:hover .brz-forms2__radio, .brz .brz-css-du33fw .brz-forms2__checkbox {color: rgba(115,119,127,.7);}
	.brz .brz-css-du33fw .brz-forms2__checkbox-option-name, .brz-forms2__radio-option-name {text-transform: inherit !important;}
	.brz .brz-css-du33fw:hover .brz-forms2__select-item__input {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-du33fw .form-alert {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);}}
@media (min-width:991px) {.brz .brz-css-du33fw:hover .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox) {transition-duration: .5s;}
	.brz .brz-css-du33fw:hover .brz-forms2__radio, .brz .brz-css-du33fw .brz-forms2__checkbox {transition-duration: .5s;}
	.brz .brz-css-du33fw:hover .brz-forms2__select-item__input {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-du33fw {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);}
	.brz .brz-css-du33fw .brz-forms2__field {font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}
	.brz .brz-css-du33fw .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox):not(.brz-forms2__field-select) {padding: 14px 24px;}
	.brz .brz-css-du33fw .brz-forms2__field-paragraph {line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);}}
@media (max-width:767px) {.brz .brz-css-du33fw {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);}
	.brz .brz-css-du33fw .brz-forms2__field {font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}
	.brz .brz-css-du33fw .brz-forms2__field:not(.brz-forms2__radio):not(.brz-forms2__checkbox):not(.brz-forms2__field-select) {padding: 14px 24px;}
	.brz .brz-css-du33fw .brz-forms2__field-paragraph {line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);}}
.brz .brz-css-1izx5n0 .select2-dropdown {box-shadow: none;}
.brz .brz-css-1izx5n0 .select2-results__options {font-family: "Lato",sans-serif;font-size: 18px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;border-radius: 0px;}
.brz .brz-css-1izx5n0 .select2-results__options {color: rgba(115,119,127,.7);background-color: rgba(255,255,255,1);}
.brz .brz-css-1izx5n0 .select2-results__option {border: 1px solid rgba(220,222,225,1);}
.brz .brz-css-1izx5n0 .select2-selection--single {padding: 14px 24px 14px 24px;padding: 14px 24px;}
.brz .brz-css-1izx5n0 .select2-selection--multiple {padding: 14px 24px 14px 24px;padding: 14px 24px;}
.brz .brz-css-1izx5n0 .select2-selection--multiple .select2-selection__choice {background-color: rgba(255,255,255,1);}
@media (min-width:991px) {.brz .brz-css-1izx5n0 .select2-selection--multiple .select2-selection__choice {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1izx5n0 .select2-results__options {font-size: 14px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1izx5n0 .select2-selection--multiple .select2-selection__choice {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-1izx5n0 .select2-results__options {font-size: 14px;}
	.brz .brz-css-1izx5n0 .select2-selection--single {padding: 10px 20px 10px 20px;padding: 10px 20px;}
	.brz .brz-css-1izx5n0 .select2-selection--multiple {padding: 10px 20px 10px 20px;padding: 10px 20px;}}
@media (max-width:767px) {.brz .brz-css-1izx5n0 .select2-selection--multiple .select2-selection__choice {transition-duration: .5s;}}
.brz .brz-css-8gy64t .select2-results__options {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
.brz .brz-css-8gy64t .select2-selection--single {padding: 20px 30px 20px 30px;padding: 20px 30px;}
.brz .brz-css-8gy64t .select2-selection--multiple {padding: 20px 30px 20px 30px;padding: 20px 30px;}
@media (min-width:991px) {.brz .brz-css-8gy64t:hover .select2-dropdown {box-shadow: none;}
	.brz .brz-css-8gy64t .select2-results__options {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;border-radius: 0px;}
	.brz .brz-css-8gy64t:hover .select2-results__options {color: rgba(115,119,127,.7);background-color: rgba(255,255,255,1);}
	.brz .brz-css-8gy64t:hover .select2-results__option {border: 1px solid rgba(220,222,225,1);}
	.brz .brz-css-8gy64t .select2-selection--single {padding: 20px 30px 20px 30px;padding: 20px 30px;}
	.brz .brz-css-8gy64t .select2-selection--multiple {padding: 20px 30px 20px 30px;padding: 20px 30px;}
	.brz .brz-css-8gy64t:hover .select2-selection--multiple .select2-selection__choice {background-color: rgba(255,255,255,1);}}
@media (min-width:991px) {.brz .brz-css-8gy64t:hover .select2-selection--multiple .select2-selection__choice {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-8gy64t .select2-results__options {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}
	.brz .brz-css-8gy64t .select2-selection--single {padding: 14px 24px 14px 24px;padding: 14px 24px;}
	.brz .brz-css-8gy64t .select2-selection--multiple {padding: 14px 24px 14px 24px;padding: 14px 24px;}}
@media (max-width:767px) {.brz .brz-css-8gy64t .select2-results__options {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}
	.brz .brz-css-8gy64t .select2-selection--single {padding: 14px 24px 14px 24px;padding: 14px 24px;}
	.brz .brz-css-8gy64t .select2-selection--multiple {padding: 14px 24px 14px 24px;padding: 14px 24px;}}
.brz .brz-css-udh86i {max-width: 100%;flex-basis: 100%;}
.brz .brz-css-udh86i .brz-textarea {height: auto;}
.brz .brz-css-udh86i .brz-forms2__checkbox-option, .brz .brz-css-udh86i .brz-forms2__radio-option {flex-basis: 100%;}
.brz .brz-css-3xinmt {max-width: 50%;flex-basis: 50%;}
@media (min-width:991px) {.brz .brz-css-3xinmt {max-width: 50%;flex-basis: 50%;}
	.brz .brz-css-3xinmt .brz-textarea {height: auto;}
	.brz .brz-css-3xinmt .brz-forms2__checkbox-option, .brz .brz-css-3xinmt .brz-forms2__radio-option {flex-basis: 100%;}}
@media (max-width:767px) {.brz .brz-css-3xinmt {max-width: 100%;flex-basis: 100%;}}
.brz .brz-css-1v1bre7 {max-width: 50%;flex-basis: 50%;}
@media (min-width:991px) {.brz .brz-css-1v1bre7 {max-width: 50%;flex-basis: 50%;}
	.brz .brz-css-1v1bre7 .brz-textarea {height: auto;}
	.brz .brz-css-1v1bre7 .brz-forms2__checkbox-option, .brz .brz-css-1v1bre7 .brz-forms2__radio-option {flex-basis: 100%;}}
@media (max-width:767px) {.brz .brz-css-1v1bre7 {max-width: 100%;flex-basis: 100%;}}
.brz .brz-css-10297kk {max-width: 50%;flex-basis: 50%;}
@media (min-width:991px) {.brz .brz-css-10297kk {max-width: 50%;flex-basis: 50%;}
	.brz .brz-css-10297kk .brz-textarea {height: auto;}
	.brz .brz-css-10297kk .brz-forms2__checkbox-option, .brz .brz-css-10297kk .brz-forms2__radio-option {flex-basis: 100%;}}
@media (max-width:767px) {.brz .brz-css-10297kk {max-width: 100%;flex-basis: 100%;}}
.brz .brz-css-azhk2l {max-width: 50%;flex-basis: 50%;}
@media (min-width:991px) {.brz .brz-css-azhk2l {max-width: 50%;flex-basis: 50%;}
	.brz .brz-css-azhk2l .brz-textarea {height: auto;}
	.brz .brz-css-azhk2l .brz-forms2__checkbox-option, .brz .brz-css-azhk2l .brz-forms2__radio-option {flex-basis: 100%;}}
@media (max-width:767px) {.brz .brz-css-azhk2l {max-width: 100%;flex-basis: 100%;}}
.brz .brz-css-k82x81 {max-width: 50%;flex-basis: 50%;}
@media (min-width:991px) {.brz .brz-css-k82x81 {max-width: 50%;flex-basis: 50%;}
	.brz .brz-css-k82x81 .brz-textarea {height: auto;}
	.brz .brz-css-k82x81 .brz-forms2__checkbox-option, .brz .brz-css-k82x81 .brz-forms2__radio-option {flex-basis: 100%;}}
@media (max-width:767px) {.brz .brz-css-k82x81 {max-width: 100%;flex-basis: 100%;}}
.brz .brz-css-c1e7i6 .brz-textarea {height: 165px !important;}
@media (min-width:991px) {.brz .brz-css-c1e7i6 {max-width: 100%;flex-basis: 100%;}
	.brz .brz-css-c1e7i6 .brz-textarea {height: 165px !important;}
	.brz .brz-css-c1e7i6 .brz-forms2__checkbox-option, .brz .brz-css-c1e7i6 .brz-forms2__radio-option {flex-basis: 100%;}}
.brz .brz-css-i4nkhk.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 0;padding: 14px 42px 14px 42px;padding: 14px 42px;flex-flow: row-reverse nowrap;}
.brz .brz-css-i4nkhk.brz-btn {display: flex;color: rgba(var(--brz-global-color8),1);border: 2px solid rgba(var(--brz-global-color3),1);box-shadow: none;}
.brz .brz-css-i4nkhk.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-i4nkhk.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-i4nkhk.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-i4nkhk .brz-btn--story-container {border: 2px solid rgba(var(--brz-global-color3),1);flex-flow: row-reverse nowrap;border-radius: 0;}
.brz .brz-css-i4nkhk .brz-btn--story-container:after {height: unset;}
@media (min-width:991px) {.brz .brz-css-i4nkhk.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (min-width:991px) {.brz .brz-css-i4nkhk.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-i4nkhk.brz-btn.brz-btn-submit:hover {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-i4nkhk.brz-btn {font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);font-size: var(--brz-buttontabletfontsize,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-i4nkhk.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-i4nkhk.brz-btn {font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);font-size: var(--brz-buttonmobilefontsize,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:767px) {.brz .brz-css-i4nkhk.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-i4nkhk .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-1np728w.brz-btn {border-radius: 16px;padding: 18px 42px 18px 42px;padding: 18px 42px;}
.brz .brz-css-1np728w.brz-btn {border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1np728w .brz-btn--story-container {border: 0px solid rgba(35,157,219,0);border-radius: 16px;}
@media (min-width:991px) {.brz .brz-css-1np728w.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 16px;padding: 18px 42px 18px 42px;padding: 18px 42px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1np728w.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(17,90,47,.8);box-shadow: none;}
	.brz .brz-css-1np728w.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-1np728w.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1np728w.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-1np728w .brz-btn--story-container {border: 0px solid rgba(17,90,47,.8);flex-flow: row-reverse nowrap;border-radius: 16px;}
	.brz .brz-css-1np728w .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1np728w.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1np728w.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1np728w.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1np728w .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1np728w.brz-btn {border-radius: 0px;padding: 14px 42px 14px 42px;padding: 14px 42px;}
	.brz .brz-css-1np728w .brz-btn--story-container {border-radius: 0px;}}
@media (max-width:767px) {.brz .brz-css-1np728w.brz-btn {border-radius: 0px;padding: 14px 42px 14px 42px;padding: 14px 42px;}
	.brz .brz-css-1np728w .brz-btn--story-container {border-radius: 0px;}}
.brz .brz-css-1qt9xmy {flex: 1 1 37%;max-width: 37%;}
.brz .brz-css-1qt9xmy > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1qt9xmy {z-index: auto;flex: 1 1 37%;max-width: 37%;justify-content: flex-start;}
	.brz .brz-css-1qt9xmy .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-1qt9xmy > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-1qt9xmy:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1qt9xmy > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1qt9xmy:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1qt9xmy:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1qt9xmy > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1qt9xmy:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1qt9xmy > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1qt9xmy:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1qt9xmy > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1qt9xmy:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1qt9xmy:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1qt9xmy:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1qt9xmy:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1qt9xmy {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1qt9xmy > .brz-bg {margin: 12px 0px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1qt9xmy {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1qt9xmy > .brz-bg {margin: 48px 0px 24px 0px;}}
.brz .brz-css-1pfde6n {margin: 0;padding: 0px 5px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1pfde6n {z-index: auto;margin: 0;border: 0px solid transparent;padding: 0px 5px 0px 0px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1pfde6n:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1pfde6n {margin: 12px 0px 0px 0px;padding: 35px 5px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1pfde6n {margin: 48px 0px 24px 0px;padding: 0;}}
@media (min-width:991px) {.brz .brz-css-144sj08 {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-144sj08 > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-144sj08:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-144sj08 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-144sj08:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-144sj08:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-144sj08 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-144sj08:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-144sj08 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-144sj08:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-144sj08 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-144sj08:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-144sj08:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-144sj08 {min-height: auto;display: flex;}
	.brz .brz-css-144sj08:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-144sj08:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-144sj08:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-144sj08:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-144sj08:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-13q9k97 {padding: 0;max-width: 100%;}}
.brz .brz-css-193bbju {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-193bbju > .brz-bg {margin: 0;}
.brz .brz-css-193bbju > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-193bbju {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-193bbju .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-193bbju > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-193bbju:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-193bbju > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-193bbju:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-193bbju:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-193bbju > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-193bbju:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
	.brz .brz-css-193bbju > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-193bbju:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-193bbju > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-193bbju:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-193bbju:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-193bbju:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-193bbju:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-lhglmv {margin: 0;padding: 48px 32px 40px 32px;}
@media (min-width:991px) {.brz .brz-css-lhglmv {z-index: auto;margin: 0;border: 0px solid transparent;padding: 48px 32px 40px 32px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-lhglmv:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-lhglmv {padding: 25px;}}
@media (max-width:767px) {.brz .brz-css-lhglmv {padding: 25px;}}
.brz .brz-css-1m6o2vs {margin: 0;justify-content: center;}
@media (min-width:991px) {.brz .brz-css-1m6o2vs {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1m6o2vs .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1m6o2vs {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1m6o2vs {margin: 10px 0px 10px 0px;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-1m6o2vs {margin: 10px 0px 10px 0px;}}
.brz .brz-css-1eu5ryl:not(.brz-image--hovered) {max-width: 100%;}
.brz .brz-css-1eu5ryl {height: auto;border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-1eu5ryl {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-1eu5ryl .brz-picture:after {border-radius: 0px;}
.brz .brz-css-1eu5ryl .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
.brz .brz-css-1eu5ryl .brz-picture {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1eu5ryl .brz-picture {filter: none;}
@media (min-width:991px) {.brz .brz-css-1eu5ryl {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl.brz-image--withHover img.brz-img, .brz .brz-css-1eu5ryl.brz-image--withHover img.dynamic-image, .brz .brz-css-1eu5ryl.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1eu5ryl:not(.brz-image--hovered) {max-width: 45%;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1eu5ryl {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl.brz-image--withHover img.brz-img, .brz .brz-css-1eu5ryl.brz-image--withHover img.dynamic-image, .brz .brz-css-1eu5ryl.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-1eu5ryl {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1eu5ryl.brz-image--withHover img.brz-img, .brz .brz-css-1eu5ryl.brz-image--withHover img.dynamic-image, .brz .brz-css-1eu5ryl.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-1oppknj {border-radius: 12px;}
.brz .brz-css-1oppknj .brz-picture:after {border-radius: 12px;}
@media (min-width:991px) {.brz .brz-css-1oppknj:not(.brz-image--hovered) {max-width: 100%;}
	.brz .brz-css-1oppknj {height: auto;border-radius: 12px;mix-blend-mode: normal;}
	.brz .brz-css-1oppknj:hover {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-1oppknj .brz-picture:after {border-radius: 12px;}
	.brz .brz-css-1oppknj:hover .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
	.brz .brz-css-1oppknj .brz-picture {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1oppknj:hover .brz-picture {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1oppknj:hover {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1oppknj:hover .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1oppknj:hover .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1oppknj.brz-image--withHover img.brz-img, .brz .brz-css-1oppknj.brz-image--withHover img.dynamic-image, .brz .brz-css-1oppknj.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-1rya7ju.brz-hover-animation__container {max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1rya7ju.brz-hover-animation__container {max-width: 45%;}}
@media (min-width:991px) {.brz .brz-css-1p6ppk0.brz-hover-animation__container {max-width: 100%;}}
.brz .brz-css-448k3s {padding-top: 28.7991%;}
.brz .brz-css-448k3s > .brz-img {position: absolute;width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-448k3s {padding-top: 12.9593%;}}
@media (max-width:767px) {.brz .brz-css-448k3s {padding-top: 28.8%;}}
.brz .brz-css-uii6g2 {padding-top: 0;}
.brz .brz-css-uii6g2 > .brz-img {position: inherit;}
@media (min-width:991px) {.brz .brz-css-uii6g2 {padding-top: 0;}
	.brz .brz-css-uii6g2 > .brz-img {position: inherit;width: 100%;}}
.brz .brz-css-1qozmh3 {width: 947.6px;height: 272.91px;margin-left: -291.85px;margin-top: 0px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1qozmh3 {width: 1856.67px;height: 534.72px;margin-left: -571.83px;}}
@media (max-width:767px) {.brz .brz-css-1qozmh3 {width: 989.51px;height: 284.98px;margin-left: -304.75px;}}
.brz .brz-css-uty09i {width: 100%;height: auto;margin-left: auto;margin-top: auto;}
@media (min-width:991px) {.brz .brz-css-uty09i {width: 100%;height: auto;margin-left: auto;margin-top: auto;}}
@media (min-width:991px) {.brz .brz-css-sn76wk {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-sn76wk .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-sn76wk {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-10fdezb {height: 35px;}
@media (min-width:991px) {.brz .brz-css-10fdezb {height: 35px;}}
.brz .brz-css-1cuti6p {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1cuti6p {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1cuti6p .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1cuti6p {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-msar9s {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-zZ04J {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-abovetitlefontfamily,initial) !important;font-size: var(--brz-abovetitlefontsize,initial);line-height: var(--brz-abovetitlelineheight,initial);font-weight: var(--brz-abovetitlefontweight,initial);font-weight: var(--brz-abovetitlebold,initial);letter-spacing: var(--brz-abovetitleletterspacing,initial);font-variation-settings: var(--brz-abovetitlefontvariation,initial);font-style: var(--brz-abovetitleitalic,initial);text-decoration: var(--brz-abovetitletextdecoration,initial) !important;text-transform: var(--brz-abovetitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-zZ04J {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-abovetitlefontfamily,initial) !important;font-size: var(--brz-abovetitlefontsize,initial);line-height: var(--brz-abovetitlelineheight,initial);font-weight: var(--brz-abovetitlefontweight,initial);font-weight: var(--brz-abovetitlebold,initial);letter-spacing: var(--brz-abovetitleletterspacing,initial);font-variation-settings: var(--brz-abovetitlefontvariation,initial);font-style: var(--brz-abovetitleitalic,initial);text-decoration: var(--brz-abovetitletextdecoration,initial) !important;text-transform: var(--brz-abovetitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-zZ04J {font-size: var(--brz-abovetitletabletfontsize,initial);line-height: var(--brz-abovetitletabletlineheight,initial);font-weight: var(--brz-abovetitletabletfontweight,initial);font-weight: var(--brz-abovetitletabletbold,initial);letter-spacing: var(--brz-abovetitletabletletterspacing,initial);font-variation-settings: var(--brz-abovetitletabletfontvariation,initial);font-style: var(--brz-abovetitletabletitalic,initial);text-decoration: var(--brz-abovetitletablettextdecoration,initial) !important;text-transform: var(--brz-abovetitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-zZ04J {font-size: var(--brz-abovetitlemobilefontsize,initial);line-height: var(--brz-abovetitlemobilelineheight,initial);font-weight: var(--brz-abovetitlemobilefontweight,initial);font-weight: var(--brz-abovetitlemobilebold,initial);letter-spacing: var(--brz-abovetitlemobileletterspacing,initial);font-variation-settings: var(--brz-abovetitlemobilefontvariation,initial);font-style: var(--brz-abovetitlemobileitalic,initial);text-decoration: var(--brz-abovetitlemobiletextdecoration,initial) !important;text-transform: var(--brz-abovetitlemobiletexttransform,initial) !important;}}
.brz .brz-css-lJ9Ib {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-abovetitlefontfamily,initial) !important;font-size: var(--brz-abovetitlefontsize,initial);line-height: var(--brz-abovetitlelineheight,initial);font-weight: var(--brz-abovetitlefontweight,initial);font-weight: var(--brz-abovetitlebold,initial);letter-spacing: var(--brz-abovetitleletterspacing,initial);font-variation-settings: var(--brz-abovetitlefontvariation,initial);font-style: var(--brz-abovetitleitalic,initial);text-decoration: var(--brz-abovetitletextdecoration,initial) !important;text-transform: var(--brz-abovetitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-lJ9Ib {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-abovetitlefontfamily,initial) !important;font-size: var(--brz-abovetitlefontsize,initial);line-height: var(--brz-abovetitlelineheight,initial);font-weight: var(--brz-abovetitlefontweight,initial);font-weight: var(--brz-abovetitlebold,initial);letter-spacing: var(--brz-abovetitleletterspacing,initial);font-variation-settings: var(--brz-abovetitlefontvariation,initial);font-style: var(--brz-abovetitleitalic,initial);text-decoration: var(--brz-abovetitletextdecoration,initial) !important;text-transform: var(--brz-abovetitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-lJ9Ib {font-size: var(--brz-abovetitletabletfontsize,initial);line-height: var(--brz-abovetitletabletlineheight,initial);font-weight: var(--brz-abovetitletabletfontweight,initial);font-weight: var(--brz-abovetitletabletbold,initial);letter-spacing: var(--brz-abovetitletabletletterspacing,initial);font-variation-settings: var(--brz-abovetitletabletfontvariation,initial);font-style: var(--brz-abovetitletabletitalic,initial);text-decoration: var(--brz-abovetitletablettextdecoration,initial) !important;text-transform: var(--brz-abovetitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-lJ9Ib {font-size: var(--brz-abovetitlemobilefontsize,initial);line-height: var(--brz-abovetitlemobilelineheight,initial);font-weight: var(--brz-abovetitlemobilefontweight,initial);font-weight: var(--brz-abovetitlemobilebold,initial);letter-spacing: var(--brz-abovetitlemobileletterspacing,initial);font-variation-settings: var(--brz-abovetitlemobilefontvariation,initial);font-style: var(--brz-abovetitlemobileitalic,initial);text-decoration: var(--brz-abovetitlemobiletextdecoration,initial) !important;text-transform: var(--brz-abovetitlemobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1m137ye {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1m137ye .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1m137ye {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-t567n6 {height: 35px;}
@media (min-width:991px) {.brz .brz-css-t567n6 {height: 35px;}}
.brz .brz-css-1diwqgn {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1diwqgn {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1diwqgn .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1diwqgn {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-1hwd4tc {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-dB38u {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-abovetitlefontfamily,initial) !important;font-size: var(--brz-abovetitlefontsize,initial);line-height: var(--brz-abovetitlelineheight,initial);font-weight: var(--brz-abovetitlefontweight,initial);font-weight: var(--brz-abovetitlebold,initial);letter-spacing: var(--brz-abovetitleletterspacing,initial);font-variation-settings: var(--brz-abovetitlefontvariation,initial);font-style: var(--brz-abovetitleitalic,initial);text-decoration: var(--brz-abovetitletextdecoration,initial) !important;text-transform: var(--brz-abovetitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-dB38u {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-abovetitlefontfamily,initial) !important;font-size: var(--brz-abovetitlefontsize,initial);line-height: var(--brz-abovetitlelineheight,initial);font-weight: var(--brz-abovetitlefontweight,initial);font-weight: var(--brz-abovetitlebold,initial);letter-spacing: var(--brz-abovetitleletterspacing,initial);font-variation-settings: var(--brz-abovetitlefontvariation,initial);font-style: var(--brz-abovetitleitalic,initial);text-decoration: var(--brz-abovetitletextdecoration,initial) !important;text-transform: var(--brz-abovetitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-dB38u {font-size: var(--brz-abovetitletabletfontsize,initial);line-height: var(--brz-abovetitletabletlineheight,initial);font-weight: var(--brz-abovetitletabletfontweight,initial);font-weight: var(--brz-abovetitletabletbold,initial);letter-spacing: var(--brz-abovetitletabletletterspacing,initial);font-variation-settings: var(--brz-abovetitletabletfontvariation,initial);font-style: var(--brz-abovetitletabletitalic,initial);text-decoration: var(--brz-abovetitletablettextdecoration,initial) !important;text-transform: var(--brz-abovetitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-dB38u {font-size: var(--brz-abovetitlemobilefontsize,initial);line-height: var(--brz-abovetitlemobilelineheight,initial);font-weight: var(--brz-abovetitlemobilefontweight,initial);font-weight: var(--brz-abovetitlemobilebold,initial);letter-spacing: var(--brz-abovetitlemobileletterspacing,initial);font-variation-settings: var(--brz-abovetitlemobilefontvariation,initial);font-style: var(--brz-abovetitlemobileitalic,initial);text-decoration: var(--brz-abovetitlemobiletextdecoration,initial) !important;text-transform: var(--brz-abovetitlemobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1tofl41 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1tofl41 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1tofl41 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1g8p7s5 {height: 35px;}
@media (min-width:991px) {.brz .brz-css-1g8p7s5 {height: 35px;}}
.brz .brz-css-9s4nrj {z-index: auto;position: relative;margin: 10px 0px 10px 0px;justify-content: center;padding: 0;gap: 20px 10px;}
@media (min-width:991px) {.brz .brz-css-9s4nrj {position: relative;}
	.brz .brz-css-9s4nrj {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-9s4nrj {position: relative;}
	.brz .brz-css-9s4nrj {display: flex;}}
@media (max-width:767px) {.brz .brz-css-9s4nrj {position: relative;}
	.brz .brz-css-9s4nrj {display: flex;}}
.brz .brz-css-mnqhml {margin: 0;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-mnqhml {z-index: auto;position: relative;margin: 0;justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-mnqhml {position: relative;}
	.brz .brz-css-mnqhml:hover {display: flex;}}
.brz .brz-css-139uu97 {justify-content: center;padding: 0;gap: 20px 10px;}
.brz .brz-css-ps0ovm {justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-ps0ovm {justify-content: flex-start;padding: 0;gap: 20px 10px;}}
.brz .brz-css-1cdczpf {font-size: 48px;padding: 0px;border-radius: 0;stroke-width: 1;}
.brz .brz-css-1cdczpf {color: rgba(var(--brz-global-color3),1);border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}
.brz .brz-css-1cdczpf .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-1cdczpf {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1cdczpf .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-1cdczpf:hover {color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-1cdczpf:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1cdczpf {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1cdczpf .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:767px) {.brz .brz-css-1cdczpf {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1cdczpf .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-2mv7o5 {font-size: 15px;padding: 14px;border-radius: 100px;}
.brz .brz-css-2mv7o5 {background-color: rgba(var(--brz-global-color8),1);}
@media (min-width:991px) {.brz .brz-css-2mv7o5 {font-size: 15px;padding: 14px;border-radius: 100px;stroke-width: 1;}
	.brz .brz-css-2mv7o5:hover {color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(var(--brz-global-color3),1);box-shadow: none;background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-2mv7o5:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (min-width:991px) {.brz .brz-css-2mv7o5:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-2mv7o5:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-1u2geql {font-size: 15px;padding: 14px;border-radius: 100px;}
.brz .brz-css-1u2geql {background-color: rgba(var(--brz-global-color8),1);}
@media (min-width:991px) {.brz .brz-css-1u2geql {font-size: 15px;padding: 14px;border-radius: 100px;stroke-width: 1;}
	.brz .brz-css-1u2geql:hover {color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(var(--brz-global-color3),1);box-shadow: none;background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-1u2geql:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (min-width:991px) {.brz .brz-css-1u2geql:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1u2geql:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-sk5zf3 {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sk5zf3 {display: block;}}
@media (max-width:767px) {.brz .brz-css-sk5zf3 {display: block;}}
@media (min-width:991px) {.brz .brz-css-c8fq4o:hover {display: block;}}
.brz .brz-css-1wzn4p1 {padding: 75px 0px 75px 0px;margin: 0;}
.brz .brz-css-1wzn4p1 > .brz-bg {border-radius: 0px;}
.brz .brz-css-1wzn4p1 > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-1wzn4p1 > .brz-bg:after {box-shadow: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-image {-webkit-mask-image: none;mask-image: none;background-size: cover;background-repeat: no-repeat;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-image {background-image: none;filter: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1wzn4p1 {padding: 50px 15px 50px 15px;}}
@media (max-width:767px) {.brz .brz-css-1wzn4p1 {padding: 25px 15px 25px 15px;}}
.brz .brz-css-1uopts9 {padding: 0;margin: 0px 48px 0px 48px;}
.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-color {background-color: rgba(17,90,47,0);}
@media (min-width:991px) {.brz .brz-css-1uopts9 {padding: 0;margin: 0px 48px 0px 48px;}
	.brz .brz-css-1uopts9 > .brz-bg {border-radius: 0px;}
	.brz .brz-css-1uopts9:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-1uopts9:hover > .brz-bg:after {box-shadow: none;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-image {-webkit-mask-image: none;mask-image: none;background-size: cover;background-repeat: no-repeat;}
	.brz .brz-css-1uopts9:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;}
	.brz .brz-css-1uopts9:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1uopts9:hover > .brz-bg > .brz-bg-color {background-color: rgba(17,90,47,0);background-image: none;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1uopts9 {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-1uopts9 {margin: 0;}}
.brz .brz-css-2gpbzd {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-2gpbzd {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-2gpbzd {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-2gpbzd {max-width: 100%;}}
@media (min-width:991px) {.brz .brz-css-1efqzy4:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1efqzy4 {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-1t66ezt {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-1t66ezt > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-1t66ezt > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-1t66ezt > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1t66ezt {min-height: auto;display: flex;}
	.brz .brz-css-1t66ezt > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1t66ezt {min-height: auto;display: flex;}
	.brz .brz-css-1t66ezt > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-1t66ezt {min-height: auto;display: flex;}
	.brz .brz-css-1t66ezt > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
.brz .brz-css-1a7ogzm > .brz-bg {border-radius: 16px;}
.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);}
@media (min-width:991px) {.brz .brz-css-1a7ogzm {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-1a7ogzm > .brz-bg {border-radius: 16px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);background-image: none;}
	.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1a7ogzm {min-height: auto;display: flex;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1a7ogzm:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1a7ogzm > .brz-bg {border-radius: 0px;}}
@media (max-width:767px) {.brz .brz-css-1a7ogzm > .brz-bg {border-radius: 0px;}}
.brz .brz-css-lt4404 {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-lt4404 {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-lt4404 {padding: 0;}}
.brz .brz-css-18oneyz {padding: 8px 40px 8px 40px;}
@media (min-width:991px) {.brz .brz-css-18oneyz {padding: 8px 40px 8px 40px;max-width: 100%;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-18oneyz {padding: 8px 32px 8px 24px;}}
@media (max-width:767px) {.brz .brz-css-18oneyz {padding: 8px 24px 8px 16px;}}
.brz .brz-css-1csnpdv {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-1csnpdv .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-1csnpdv > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-1csnpdv > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-1csnpdv > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1csnpdv > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1csnpdv {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1csnpdv > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-1csnpdv > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-88dicl {flex: 1 1 26%;max-width: 26%;justify-content: center;}
.brz .brz-css-88dicl .brz-columns__scroll-effect {justify-content: center;}
.brz .brz-css-88dicl > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-88dicl {z-index: auto;flex: 1 1 26%;max-width: 26%;justify-content: center;}
	.brz .brz-css-88dicl .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-88dicl > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-88dicl:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-88dicl > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-88dicl > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-88dicl > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-88dicl > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-88dicl:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-88dicl {flex: 1 1 37.3%;max-width: 37.3%;}}
@media (max-width:767px) {.brz .brz-css-88dicl {flex: 1 1 60%;max-width: 60%;}}
.brz .brz-css-1pzte4r {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-1pzte4r {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1pzte4r {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1pzte4r {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1pzte4r {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-znagm0 {margin: 0;padding: 0;}
@media (min-width:991px) {.brz .brz-css-znagm0 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 0;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-znagm0:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1tjbyob {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-1tjbyob .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-1tjbyob {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tjbyob {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1tjbyob {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1puqgto {margin: 0;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-1puqgto {padding: 0;margin: 0;justify-content: flex-start;position: relative;}
	.brz .brz-css-1puqgto .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1puqgto {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-q31jzm:not(.brz-image--hovered) {max-width: 100%;}
.brz .brz-css-q31jzm {height: auto;border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-q31jzm {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-q31jzm .brz-picture:after {border-radius: 0px;}
.brz .brz-css-q31jzm .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
.brz .brz-css-q31jzm .brz-picture {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-q31jzm .brz-picture {filter: none;}
@media (min-width:991px) {.brz .brz-css-q31jzm {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm.brz-image--withHover img.brz-img, .brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image, .brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-q31jzm {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm.brz-image--withHover img.brz-img, .brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image, .brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-q31jzm {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm.brz-image--withHover img.brz-img, .brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image, .brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (min-width:991px) {.brz .brz-css-yp9xav:not(.brz-image--hovered) {max-width: 100%;}
	.brz .brz-css-yp9xav {height: auto;border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-yp9xav:hover {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-yp9xav .brz-picture:after {border-radius: 0px;}
	.brz .brz-css-yp9xav:hover .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
	.brz .brz-css-yp9xav .brz-picture {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-yp9xav:hover .brz-picture {filter: none;}}
@media (min-width:991px) {.brz .brz-css-yp9xav:hover {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-yp9xav:hover .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-yp9xav:hover .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-yp9xav.brz-image--withHover img.brz-img, .brz .brz-css-yp9xav.brz-image--withHover img.dynamic-image, .brz .brz-css-yp9xav.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-h68n6u.brz-hover-animation__container {max-width: 100%;}
@media (min-width:991px) {.brz .brz-css-ypzqsr.brz-hover-animation__container {max-width: 100%;}}
.brz .brz-css-1yykq65 {padding-top: 28.8003%;}
.brz .brz-css-1yykq65 > .brz-img {position: absolute;width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1yykq65 {padding-top: 28.7989%;}}
@media (max-width:767px) {.brz .brz-css-1yykq65 {padding-top: 28.7991%;}}
.brz .brz-css-z4zlrs {padding-top: 0;}
.brz .brz-css-z4zlrs > .brz-img {position: inherit;}
@media (min-width:991px) {.brz .brz-css-z4zlrs {padding-top: 0;}
	.brz .brz-css-z4zlrs > .brz-img {position: inherit;width: 100%;}}
.brz .brz-css-uk9miq {width: 378.89px;height: 109.12px;margin-left: -81.75px;margin-top: 0px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-uk9miq {width: 299.03px;height: 86.12px;margin-left: -64.52px;}}
@media (max-width:767px) {.brz .brz-css-uk9miq {width: 329.31px;height: 94.84px;margin-left: -71.05px;}}
.brz .brz-css-vw7hrb {width: 100%;height: auto;margin-left: auto;margin-top: auto;}
@media (min-width:991px) {.brz .brz-css-vw7hrb {width: 100%;height: auto;margin-left: auto;margin-top: auto;}}
.brz .brz-css-1t2tmqk {flex: 1 1 67.3%;max-width: 67.3%;justify-content: center;}
.brz .brz-css-1t2tmqk .brz-columns__scroll-effect {justify-content: center;}
.brz .brz-css-1t2tmqk > .brz-bg {margin: 0px 32px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1t2tmqk {z-index: auto;flex: 1 1 67.3%;max-width: 67.3%;justify-content: center;}
	.brz .brz-css-1t2tmqk .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-1t2tmqk > .brz-bg {margin: 0px 32px 0px 0px;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1t2tmqk > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1t2tmqk > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1t2tmqk > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1t2tmqk > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1t2tmqk:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1t2tmqk {flex: 1 1 61.3%;max-width: 61.3%;}
	.brz .brz-css-1t2tmqk > .brz-bg {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-1t2tmqk {flex: 1 1 40%;max-width: 40%;}
	.brz .brz-css-1t2tmqk > .brz-bg {margin: 0;}}
.brz .brz-css-kmg040 {margin: 0px 32px 0px 0px;padding: 0px 0px 0px 16px;}
@media (min-width:991px) {.brz .brz-css-kmg040 {z-index: auto;margin: 0px 32px 0px 0px;border: 0px solid transparent;padding: 0px 0px 0px 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-kmg040:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-kmg040 {margin: 0;padding: 0px 0px 0px 5px;}}
@media (max-width:767px) {.brz .brz-css-kmg040 {margin: 0;padding: 0px 8px 0px 0px;}}
.brz .brz-css-icg1tg {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-icg1tg {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;position: relative;}
	.brz .brz-css-icg1tg .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-icg1tg {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-icg1tg {padding: 0;margin: 5px 0px 5px 0px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-icg1tg {display: none;}}
@media (max-width:767px) {.brz .brz-css-icg1tg {padding: 0;margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-icg1tg {display: none;}}
@media (min-width:991px) {.brz .brz-css-19uzdaw .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-19uzdaw .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-19uzdaw .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-19uzdaw .brz-mm-menu__icon {display: flex;font-size: 18px;}
	.brz .brz-css-19uzdaw .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-19uzdaw .brz-menu {display: none;}}
@media (max-width:767px) {.brz .brz-css-19uzdaw .brz-mm-menu__icon {display: flex;font-size: 18px;}
	.brz .brz-css-19uzdaw .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-19uzdaw .brz-menu {display: none;}}
@media (min-width:991px) {.brz .brz-css-159oqwi .brz-mm-menu__icon {font-size: 18px;}
	.brz .brz-css-159oqwi .brz-mm-menu__icon {color: rgba(51,51,51,1);}}
@media (min-width:991px) {.brz .brz-css-159oqwi .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-159oqwi:hover .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-159oqwi .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-159oqwi .brz-mm-menu__icon {font-size: 38px;}
	.brz .brz-css-159oqwi .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
@media (max-width:767px) {.brz .brz-css-159oqwi .brz-mm-menu__icon {font-size: 32px;}
	.brz .brz-css-159oqwi .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
.brz .brz-css-c39cel .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -5px 0px -5px;}
.brz .brz-css-c39cel .brz-menu__ul {color: rgba(0,0,0,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(0,0,0,1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 15px 0 0;}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {color: rgba(0,0,0,1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
.brz .brz-css-c39cel .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);border-radius: 0px;}
.brz .brz-css-c39cel .brz-menu__sub-menu {color: rgba(255,255,255,1);background-color: rgba(51,51,51,1);box-shadow: none;}
.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(51,51,51,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(51,51,51,1);}
.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(51,51,51,1);color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {border-color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}
@media (min-width:991px) {.brz .brz-css-c39cel .brz-menu__ul {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-c39cel .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-c39cel [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-c39cel .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-c39cel .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-c39cel .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-c39cel .brz-mega-menu__dropdown {display: none;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown > .brz-a:after {border-right-style: solid;border-left-style: none;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-menu__sub-menu {position: relative;top: auto;left: auto;transform: translate(0,0);height: 0;overflow: hidden;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item--opened > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}
	.brz .brz-css-c39cel.brz-menu__preview .brz-menu__sub-menu .brz-menu__item > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}}
@media (max-width:767px) {.brz .brz-css-c39cel .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-c39cel .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-c39cel .brz-mega-menu__dropdown {display: block;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown > .brz-a:after {border-right-style: solid;border-left-style: none;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-menu__sub-menu {position: relative;top: auto;left: auto;transform: translate(0,0);height: 0;overflow: hidden;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item--opened > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}
	.brz .brz-css-c39cel.brz-menu__preview .brz-menu__sub-menu .brz-menu__item > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}}
.brz .brz-css-1tdvdmo .brz-menu__ul {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);margin: 0px -17.5px 0px -17.5px;}
.brz .brz-css-1tdvdmo .brz-menu__ul {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu {color: rgba(164,248,240,1);background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-a:hover {color: rgba(164,248,240,1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(164,248,240,1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(164,248,240,1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(var(--brz-global-color3),1);color: rgba(164,248,240,1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {border-color: rgba(164,248,240,1);}
@media (min-width:991px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-size: var(--brz-ugudlcdcxlbqfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -17.5px 0px -17.5px;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__ul {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 15px 0 0;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);border-radius: 0px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu:hover {color: rgba(255,255,255,1);background-color: rgba(var(--brz-global-color3),1);box-shadow: none;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item:hover {background-color: rgba(var(--brz-global-color3),1);color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {border-color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}}
@media (min-width:991px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-size: var(--brz-ugudlcdcxlbqfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-1tdvdmo [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-1tdvdmo > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-1tdvdmo > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-1tdvdmo > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-1tdvdmo .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:767px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}}
.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {color: rgba(255,255,255,1);background-color: #333;}
.brz .brz-css-12dn42o .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-12dn42o .brz-menu__item {color: rgba(255,255,255,1);border-color: rgba(85,85,85,1);}
.brz nav.brz-mm-menu.brz-css-12dn42o {background-color: rgba(51,51,51,.8);}
.brz .brz-css-12dn42o.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
.brz .brz-css-12dn42o .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o .brz-mm-navbar {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {background-color: rgba(51,51,51,.8);}
.brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(51,51,51,.8);}
.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(85,85,85,1);}
.brz .brz-css-12dn42o .brz-mm-listitem {border-color: rgba(85,85,85,1);}
.brz .brz-css-12dn42o  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
@media (min-width:991px) {.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-12dn42o .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-12dn42o .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-12dn42o {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-12dn42o .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-12dn42o .brz-menu__item {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-12dn42o .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-12dn42o {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-12dn42o .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttontabletlineheight,initial) * var(--brz-buttontabletfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-12dn42o .brz-menu__item {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-12dn42o .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-12dn42o {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-12dn42o .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonmobilelineheight,initial) * var(--brz-buttonmobilefontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-1x5bkh9 .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-1x5bkh9 .brz-menu__item {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
.brz nav.brz-mm-menu.brz-css-1x5bkh9 {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;}
.brz .brz-css-1x5bkh9 .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1x5bkh9 .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1x5bkh9 .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1x5bkh9 .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1x5bkh9 .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1x5bkh9 .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1x5bkh9 .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1x5bkh9  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1x5bkh9  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}}
@media (min-width:991px) {.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close:hover {color: rgba(255,255,255,1);background-color: #333;}
	.brz .brz-css-1x5bkh9 .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
	.brz .brz-css-1x5bkh9 .brz-menu__item:hover {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
	.brz nav.brz-mm-menu.brz-css-1x5bkh9 {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
	.brz .brz-css-1x5bkh9:hover .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1x5bkh9 .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1x5bkh9:hover .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1x5bkh9:hover .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1x5bkh9 .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9:hover .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);}
	.brz:hover .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1x5bkh9  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}}
@media (min-width:991px) {.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close:hover {transition-duration: .3s;}
	.brz .brz-css-1x5bkh9 .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1x5bkh9 .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1x5bkh9 {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9:hover .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1x5bkh9 .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1x5bkh9 .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9:hover .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1x5bkh9:hover .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9:hover .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-family: var(--brz-heading3fontfamily,initial);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-size: var(--brz-heading3tabletfontsize,initial);font-weight: var(--brz-heading3tabletfontweight,initial);font-weight: var(--brz-heading3tabletbold,initial);line-height: var(--brz-heading3tabletlineheight,initial);letter-spacing: var(--brz-heading3tabletletterspacing,initial);font-variation-settings: var(--brz-heading3tabletfontvariation,initial);font-style: var(--brz-heading3tabletitalic,initial);text-decoration: var(--brz-heading3tablettextdecoration,initial) !important;text-transform: var(--brz-heading3tablettexttransform,initial) !important;}
	.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {font-family: var(--brz-heading3fontfamily,initial);font-size: var(--brz-heading3tabletfontsize,initial);font-weight: var(--brz-heading3tabletfontweight,initial);font-weight: var(--brz-heading3tabletbold,initial);line-height: var(--brz-heading3tabletlineheight,initial);letter-spacing: var(--brz-heading3tabletletterspacing,initial);font-variation-settings: var(--brz-heading3tabletfontvariation,initial);font-style: var(--brz-heading3tabletitalic,initial);text-decoration: var(--brz-heading3tablettextdecoration,initial) !important;text-transform: var(--brz-heading3tablettexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading3tabletlineheight,initial) * var(--brz-heading3tabletfontsize,initial) + 15px + 15px);padding-right: 20px;}}
@media (max-width:767px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-family: var(--brz-heading3fontfamily,initial);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}}
@media (max-width:767px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-size: var(--brz-heading3mobilefontsize,initial);font-weight: var(--brz-heading3mobilefontweight,initial);font-weight: var(--brz-heading3mobilebold,initial);line-height: var(--brz-heading3mobilelineheight,initial);letter-spacing: var(--brz-heading3mobileletterspacing,initial);font-variation-settings: var(--brz-heading3mobilefontvariation,initial);font-style: var(--brz-heading3mobileitalic,initial);text-decoration: var(--brz-heading3mobiletextdecoration,initial) !important;text-transform: var(--brz-heading3mobiletexttransform,initial) !important;}
	.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {font-family: var(--brz-heading3fontfamily,initial);font-size: var(--brz-heading3mobilefontsize,initial);font-weight: var(--brz-heading3mobilefontweight,initial);font-weight: var(--brz-heading3mobilebold,initial);line-height: var(--brz-heading3mobilelineheight,initial);letter-spacing: var(--brz-heading3mobileletterspacing,initial);font-variation-settings: var(--brz-heading3mobilefontvariation,initial);font-style: var(--brz-heading3mobileitalic,initial);text-decoration: var(--brz-heading3mobiletextdecoration,initial) !important;text-transform: var(--brz-heading3mobiletexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading3mobilelineheight,initial) * var(--brz-heading3mobilefontsize,initial) + 15px + 15px);padding-right: 20px;}}
.brz .brz-css-19ioiov {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-19ioiov {display: none;}}
@media (min-width:991px) {.brz .brz-css-19ioiov {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;position: relative;}
	.brz .brz-css-19ioiov .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-19ioiov {display: flex;display: none;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-19ioiov {padding: 0;margin: 5px 0px 5px 0px;}}
@media (max-width:767px) {.brz .brz-css-19ioiov {padding: 0;margin: 10px 0px 10px 0px;}}
@media (min-width:991px) {.brz .brz-css-1mxc0dk .brz-mm-menu__icon {font-size: 18px;}
	.brz .brz-css-1mxc0dk .brz-mm-menu__icon {color: rgba(51,51,51,1);}}
@media (min-width:991px) {.brz .brz-css-1mxc0dk .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-1mxc0dk:hover .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1mxc0dk .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1mxc0dk .brz-mm-menu__icon {font-size: 28px;}
	.brz .brz-css-1mxc0dk .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
@media (max-width:767px) {.brz .brz-css-1mxc0dk .brz-mm-menu__icon {font-size: 28px;}
	.brz .brz-css-1mxc0dk .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
.brz .brz-css-1dgf3el .brz-menu__ul {font-family: var(--brz-heading4fontfamily,initial);margin: 0px -17.5px 0px -17.5px;}
.brz .brz-css-1dgf3el .brz-menu__ul {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 4px 0 0;}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-size: var(--brz-heading4fontsize,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);line-height: var(--brz-heading4lineheight,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 20px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-family: var(--brz-heading4fontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -17.5px 0px -17.5px;}
	.brz .brz-css-1dgf3el:hover .brz-menu__ul {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 4px 0 0;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);border-radius: 0px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu:hover {color: rgba(255,255,255,1);background-color: rgba(var(--brz-global-color3),1);box-shadow: none;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item:hover {background-color: rgba(var(--brz-global-color3),1);color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {border-color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}}
@media (min-width:991px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-size: var(--brz-heading4fontsize,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);line-height: var(--brz-heading4lineheight,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
	.brz .brz-css-1dgf3el:hover .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 20px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-1dgf3el [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-1dgf3el > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-1dgf3el > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-1dgf3el > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-1dgf3el .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:767px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}}
.brz .brz-css-1kwae1d .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-1kwae1d .brz-menu__item {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
.brz nav.brz-mm-menu.brz-css-1kwae1d {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;}
.brz .brz-css-1kwae1d .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1kwae1d .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1kwae1d .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1kwae1d .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1kwae1d .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1kwae1d .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1kwae1d .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}}
@media (min-width:991px) {.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
	.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close:hover {color: rgba(255,255,255,1);background-color: #333;}
	.brz .brz-css-1kwae1d .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
	.brz .brz-css-1kwae1d .brz-menu__item:hover {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
	.brz nav.brz-mm-menu.brz-css-1kwae1d {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
	.brz .brz-css-1kwae1d:hover .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1kwae1d .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1kwae1d .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1kwae1d:hover .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1kwae1d:hover .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1kwae1d .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d:hover .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);}
	.brz:hover .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}}
@media (min-width:991px) {.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close:hover {transition-duration: .3s;}
	.brz .brz-css-1kwae1d .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1kwae1d .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1kwae1d {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d:hover .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1kwae1d .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d:hover .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1kwae1d:hover .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d:hover .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1kwae1d .brz-menu__item {font-family: var(--brz-heading5fontfamily,initial);}
	.brz .brz-css-1kwae1d .brz-menu__item {color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}
	.brz .brz-css-1kwae1d .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d .brz-mm-navbar {color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1kwae1d .brz-menu__item {font-size: var(--brz-heading5tabletfontsize,initial);font-weight: var(--brz-heading5tabletfontweight,initial);font-weight: var(--brz-heading5tabletbold,initial);line-height: var(--brz-heading5tabletlineheight,initial);letter-spacing: var(--brz-heading5tabletletterspacing,initial);font-variation-settings: var(--brz-heading5tabletfontvariation,initial);font-style: var(--brz-heading5tabletitalic,initial);text-decoration: var(--brz-heading5tablettextdecoration,initial) !important;text-transform: var(--brz-heading5tablettexttransform,initial) !important;}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {margin: 0 4px 0 0;font-size: 20px;}
	.brz .brz-css-1kwae1d .brz-mm-navbar {font-family: var(--brz-heading5fontfamily,initial);font-size: var(--brz-heading5tabletfontsize,initial);font-weight: var(--brz-heading5tabletfontweight,initial);font-weight: var(--brz-heading5tabletbold,initial);line-height: var(--brz-heading5tabletlineheight,initial);letter-spacing: var(--brz-heading5tabletletterspacing,initial);font-variation-settings: var(--brz-heading5tabletfontvariation,initial);font-style: var(--brz-heading5tabletitalic,initial);text-decoration: var(--brz-heading5tablettextdecoration,initial) !important;text-transform: var(--brz-heading5tablettexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading5tabletlineheight,initial) * var(--brz-heading5tabletfontsize,initial) + 15px + 15px);padding-right: 20px;}}
@media (max-width:767px) {.brz .brz-css-1kwae1d .brz-menu__item {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);}
	.brz .brz-css-1kwae1d .brz-menu__item {color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}
	.brz .brz-css-1kwae1d .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d .brz-mm-navbar {color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(255,255,255,1);}
	.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}}
@media (max-width:767px) {.brz .brz-css-1kwae1d .brz-menu__item {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {margin: 0 4px 0 0;font-size: 20px;}
	.brz .brz-css-1kwae1d .brz-mm-navbar {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-ugudlcdcxlbqmobilelineheight,initial) * var(--brz-ugudlcdcxlbqmobilefontsize,initial) + 15px + 15px);padding-right: 20px;}}
.brz .brz-css-jow5dd {flex: 1 1 6.7%;max-width: 6.7%;justify-content: center;}
.brz .brz-css-jow5dd .brz-columns__scroll-effect {justify-content: center;}
@media (min-width:991px) {.brz .brz-css-jow5dd {z-index: auto;flex: 1 1 6.7%;max-width: 6.7%;justify-content: center;}
	.brz .brz-css-jow5dd .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-jow5dd > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-jow5dd:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-jow5dd > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-jow5dd > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-jow5dd > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-jow5dd > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-jow5dd:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-jow5dd > * {display: none;}
	.brz .brz-css-jow5dd > .brz-column__items {display: none;}}
@media (max-width:767px) {.brz .brz-css-jow5dd {flex: 1 1 100%;max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-jow5dd > * {display: none;}
	.brz .brz-css-jow5dd > .brz-column__items {display: none;}}
.brz .brz-css-190lc1z {padding: 0;}
@media (min-width:991px) {.brz .brz-css-190lc1z {z-index: auto;margin: 0;border: 0px solid transparent;padding: 0;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-190lc1z:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-190lc1z {padding: 5px 15px 5px 15px;}}
.brz .brz-css-xmdiqp {margin: 0;}
.brz .brz-css-xmdiqp .brz-wrapper-transform {--transform-flipHorizontal: 1;--transform-flipVertical: 1;--transform-anchor-pointX: center;--transform-anchor-pointY: center;transform: scaleX(calc(var(--transform-flipHorizontal) * 1)) scaleY(calc(var(--transform-flipVertical) * 1));transform-origin: var(--transform-anchor-pointY) var(--transform-anchor-pointX);}
@media (min-width:991px) {.brz .brz-css-xmdiqp {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-xmdiqp .brz-wrapper-transform {--transform-flipHorizontal: 1;--transform-flipVertical: 1;--transform-anchor-pointX: center;--transform-anchor-pointY: center;transform: scaleX(calc(var(--transform-flipHorizontal) * 1)) scaleY(calc(var(--transform-flipVertical) * 1));transform-origin: var(--transform-anchor-pointY) var(--transform-anchor-pointX);}}
@media (min-width:991px) {.brz .brz-css-xmdiqp {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-xmdiqp {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-xmdiqp {margin: 10px 0px 10px 0px;}}
.brz .brz-css-nntapz {flex-direction: row;}
.brz .brz-css-nntapz .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}
.brz .brz-css-1umndxb .brz-icon__container {margin-left: auto;margin-right: 2px;}
@media (min-width:991px) {.brz .brz-css-1umndxb {flex-direction: row;}
	.brz .brz-css-1umndxb .brz-icon__container {margin-left: auto;margin-right: 2px;align-items: flex-start;}}
.brz .brz-css-e9bk1k {font-size: 48px;padding: 0px;border-radius: 0;stroke-width: 1;}
.brz .brz-css-e9bk1k {color: rgba(var(--brz-global-color3),1);border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}
.brz .brz-css-e9bk1k .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-e9bk1k {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-e9bk1k .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-e9bk1k:hover {color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-e9bk1k:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-e9bk1k {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-e9bk1k .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:767px) {.brz .brz-css-e9bk1k {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-e9bk1k .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-h3q0h5 {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-h3q0h5 {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-h3q0h5:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-h3q0h5:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-h3q0h5:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-10fnxcx {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-1n6htfm {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-of8RQ {margin-top: 0px !important;margin-bottom: 0px !important;text-align: justify !important;font-family: "Comfortaa",display !important;font-size: 16px;line-height: 1.7;font-weight: 400;letter-spacing: -.2px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;}
@media (min-width:991px) {.brz .brz-css-of8RQ {margin-top: 0px !important;margin-bottom: 0px !important;text-align: justify !important;font-family: "Comfortaa",display !important;font-size: 16px;line-height: 1.7;font-weight: 400;letter-spacing: -.2px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
.brz .brz-css-r8i1ey {margin: 0;}
@media (min-width:991px) {.brz .brz-css-r8i1ey {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-r8i1ey .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-r8i1ey {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-r8i1ey {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-r8i1ey {margin: 10px 0px 10px 0px;}}
.brz .brz-css-zwba1n {width: 100%;min-height: 100%;}
.brz .brz-css-zwba1n:before {border-radius: 0px;}
.brz .brz-css-zwba1n:before {box-shadow: none;border: 0px solid rgba(220,222,225,1);}
.brz .brz-css-zwba1n .brz-embed-content {padding: 0;border-radius: 0px;overflow: hidden;}
.brz .brz-css-zwba1n .brz-embed-content {background-color: rgba(var(--brz-global-color2),0);background-image: none;}
@media (min-width:991px) {.brz .brz-css-zwba1n:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-zwba1n:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-zwba1n:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-3bcg12 {width: 100%;min-height: 100%;}
	.brz .brz-css-3bcg12:before {border-radius: 0px;}
	.brz .brz-css-3bcg12:hover:before {box-shadow: none;border: 0px solid rgba(220,222,225,1);}
	.brz .brz-css-3bcg12 .brz-embed-content {padding: 0;border-radius: 0px;overflow: hidden;}
	.brz .brz-css-3bcg12:hover .brz-embed-content {background-color: rgba(var(--brz-global-color2),0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-3bcg12:hover:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-2sdt19 {z-index: auto;margin: 0;}
.brz .brz-css-2sdt19.brz-section .brz-section__content {min-height: auto;display: flex;}
.brz .brz-css-2sdt19 .brz-container {justify-content: center;}
.brz .brz-css-2sdt19 > .slick-slider > .brz-slick-slider__dots {color: rgba(0,0,0,1);}
.brz .brz-css-2sdt19 > .slick-slider > .brz-slick-slider__arrow {color: rgba(0,0,0,.7);}
@media (min-width:991px) {.brz .brz-css-2sdt19 {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-2sdt19 {display: block;}}
@media (max-width:767px) {.brz .brz-css-2sdt19 {display: block;}}
@media (min-width:991px) {.brz .brz-css-1ij56rs {z-index: auto;margin: 0;}
	.brz .brz-css-1ij56rs.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-1ij56rs .brz-container {justify-content: center;}
	.brz .brz-css-1ij56rs > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-1ij56rs > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-1ij56rs:hover {display: block;}}
.brz .brz-css-guytnn {padding: 75px 0px 75px 0px;}
.brz .brz-css-guytnn > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-guytnn > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}
@media (min-width:991px) {.brz .brz-css-guytnn > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-guytnn {padding: 50px 15px 50px 15px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-guytnn > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-guytnn {padding: 25px 15px 25px 15px;}}
@media (max-width:767px) {.brz .brz-css-guytnn > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1xl9s1b {padding: 116px 0px 128px 0px;}
.brz .brz-css-1xl9s1b > .brz-bg {border-width: 1px 0px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),.1);}
@media (min-width:991px) {.brz .brz-css-1xl9s1b {padding: 116px 0px 128px 0px;}
	.brz .brz-css-1xl9s1b > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg {border-width: 1px 0px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),.1);background-image: none;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1xl9s1b:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1xl9s1b {padding: 75px 40px 35px 40px;}}
@media (max-width:767px) {.brz .brz-css-1xl9s1b {padding: 24px 24px 72px 24px;}}
.brz .brz-css-1luw0hj {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1luw0hj {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1luw0hj {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-1luw0hj {max-width: 100%;}}
.brz .brz-css-8dnozk {border-width: 1px 0px 0px 0px;border-style: solid;border-color: transparent;}
@media (min-width:991px) {.brz .brz-css-8dnozk:hover {border-width: 1px 0px 0px 0px;border-style: solid;border-color: transparent;}}
@media (min-width:991px) {.brz .brz-css-8dnozk {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-10e8jpe {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-10e8jpe > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-10e8jpe > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-10e8jpe > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-10e8jpe {min-height: auto;display: flex;}
	.brz .brz-css-10e8jpe > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-10e8jpe {min-height: auto;display: flex;}
	.brz .brz-css-10e8jpe > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-10e8jpe {min-height: auto;display: flex;}
	.brz .brz-css-10e8jpe > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (min-width:991px) {.brz .brz-css-1an4vvi {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-1an4vvi > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-1an4vvi:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1an4vvi > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1an4vvi > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1an4vvi > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1an4vvi > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1an4vvi:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1an4vvi {min-height: auto;display: flex;}
	.brz .brz-css-1an4vvi:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1an4vvi:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1mgbab6 {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1mgbab6 {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1mgbab6 {padding: 0;}}
.brz .brz-css-187yhb2 {padding: 0;}
@media (min-width:991px) {.brz .brz-css-1gohfum {padding: 0;max-width: 100%;}}
.brz .brz-css-1vf6zvc {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-1vf6zvc .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-1vf6zvc > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-1vf6zvc > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-1vf6zvc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1vf6zvc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1vf6zvc {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1vf6zvc > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-1vf6zvc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-llznuj > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-llznuj {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
	.brz .brz-css-llznuj .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-llznuj > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-llznuj:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-llznuj > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-llznuj > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-llznuj > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-llznuj > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-llznuj:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-m57ec7 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-m57ec7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-m57ec7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-m57ec7 {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-m57ec7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1z06xgw {margin: 0;padding: 16px;}
@media (min-width:991px) {.brz .brz-css-1z06xgw {z-index: auto;margin: 0;border: 0px solid transparent;padding: 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1z06xgw:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1z06xgw {padding: 8px;}}
@media (max-width:767px) {.brz .brz-css-1z06xgw {padding: 25px 0px 0px 0px;}}
.brz .brz-css-1u7d53e {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-1u7d53e .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-1u7d53e {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1u7d53e {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1u7d53e {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-jvzud0 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-jvzud0 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-jvzud0 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-jvzud0 {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-jvzud0 {margin: 0px 0px 8px 0px;}}
.brz .brz-css-1pjdngh {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-vuz851 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-nwoKe {margin-top: 0px !important;margin-bottom: 0px !important;text-align: right !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-nwoKe {margin-top: 0px !important;margin-bottom: 0px !important;text-align: right !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-nwoKe {font-size: var(--brz-subtitletabletfontsize,initial);line-height: var(--brz-subtitletabletlineheight,initial);font-weight: var(--brz-subtitletabletfontweight,initial);font-weight: var(--brz-subtitletabletbold,initial);letter-spacing: var(--brz-subtitletabletletterspacing,initial);font-variation-settings: var(--brz-subtitletabletfontvariation,initial);font-style: var(--brz-subtitletabletitalic,initial);text-decoration: var(--brz-subtitletablettextdecoration,initial) !important;text-transform: var(--brz-subtitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-nwoKe {text-align: justify !important;font-size: var(--brz-subtitlemobilefontsize,initial);line-height: var(--brz-subtitlemobilelineheight,initial);font-weight: var(--brz-subtitlemobilefontweight,initial);font-weight: var(--brz-subtitlemobilebold,initial);letter-spacing: var(--brz-subtitlemobileletterspacing,initial);font-variation-settings: var(--brz-subtitlemobilefontvariation,initial);font-style: var(--brz-subtitlemobileitalic,initial);text-decoration: var(--brz-subtitlemobiletextdecoration,initial) !important;text-transform: var(--brz-subtitlemobiletexttransform,initial) !important;}}
.brz .brz-css-slzu2l {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1b6471k {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1b6471k .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1b6471k {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1b6471k {display: none;}}
.brz .brz-css-z59yr {height: 50px;}
.brz .brz-css-h5jkn6 {height: 20px;}
@media (min-width:991px) {.brz .brz-css-h5jkn6 {height: 20px;}}
@media (max-width:767px) {.brz .brz-css-h5jkn6 {height: 15px;}}
.brz .brz-css-facpgg {z-index: auto;position: relative;margin: 10px 0px 10px 0px;justify-content: center;padding: 0;gap: 20px 10px;}
@media (min-width:991px) {.brz .brz-css-facpgg {position: relative;}
	.brz .brz-css-facpgg {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-facpgg {position: relative;}
	.brz .brz-css-facpgg {display: flex;}}
@media (max-width:767px) {.brz .brz-css-facpgg {position: relative;}
	.brz .brz-css-facpgg {display: flex;}}
.brz .brz-css-18y62i7 {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-18y62i7 {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-18y62i7 {position: relative;}
	.brz .brz-css-18y62i7:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-18y62i7 {justify-content: flex-start;}}
.brz .brz-css-1v6popm {justify-content: center;padding: 0;gap: 20px 10px;}
.brz .brz-css-4i66os {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-4i66os {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-4i66os {justify-content: flex-start;}}
.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1n97tm4.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1n97tm4.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
@media (min-width:991px) {.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1n97tm4.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1n97tm4.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1n97tm4.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1n97tm4.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:767px) {.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1n97tm4.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1n97tm4.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-back-pulse:before {animation-duration: .6s;}}
.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1yvwm5t.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1yvwm5t.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1yvwm5t.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1yvwm5t.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1yvwm5t.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1yvwm5t.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1yvwm5t.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-pa6524.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 0;padding: 14px 42px 14px 42px;padding: 14px 42px;flex-flow: row-reverse nowrap;}
.brz .brz-css-pa6524.brz-btn {display: flex;color: rgba(var(--brz-global-color8),1);border: 2px solid rgba(var(--brz-global-color3),1);box-shadow: none;}
.brz .brz-css-pa6524.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-pa6524.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-pa6524.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-pa6524:after {height: unset;}
.brz .brz-css-pa6524 .brz-btn--story-container {border: 2px solid rgba(var(--brz-global-color3),1);flex-flow: row-reverse nowrap;border-radius: 0;}
.brz .brz-css-pa6524 .brz-btn--story-container:after {height: unset;}
@media (min-width:991px) {.brz .brz-css-pa6524.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (min-width:991px) {.brz .brz-css-pa6524.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-pa6524.brz-btn.brz-btn-submit:hover {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-pa6524.brz-btn {font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);font-size: var(--brz-buttontabletfontsize,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-pa6524.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-pa6524.brz-btn {font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);font-size: var(--brz-buttonmobilefontsize,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:767px) {.brz .brz-css-pa6524.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-miyt54.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-miyt54.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-miyt54.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-miyt54.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-miyt54.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-miyt54 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-miyt54.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-miyt54.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-miyt54.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-miyt54.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-miyt54.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-miyt54:after {height: unset;}
	.brz .brz-css-miyt54 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-miyt54 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-miyt54.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-miyt54.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-miyt54.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-miyt54 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-miyt54.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-miyt54.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-127ypgu {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-127ypgu {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-127ypgu {position: relative;}
	.brz .brz-css-127ypgu:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-127ypgu {justify-content: flex-start;}}
.brz .brz-css-1588yx1 {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1588yx1 {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-1588yx1 {justify-content: flex-start;}}
.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-pj9c4m.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-pj9c4m.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-pj9c4m.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-pj9c4m.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-pj9c4m.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-pj9c4m.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-pj9c4m.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1ekb251.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-1ekb251.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1ekb251.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1ekb251.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1ekb251.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1ekb251 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-1ekb251.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1ekb251.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-1ekb251.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1ekb251.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1ekb251.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1ekb251:after {height: unset;}
	.brz .brz-css-1ekb251 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1ekb251 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1ekb251.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1ekb251.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1ekb251.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1ekb251 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1ekb251.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1ekb251.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-y8fpl2 {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-y8fpl2 {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-y8fpl2 {position: relative;}
	.brz .brz-css-y8fpl2:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-y8fpl2 {justify-content: flex-start;}}
.brz .brz-css-1ymmhet {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1ymmhet {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-1ymmhet {justify-content: flex-start;}}
.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1nox22q.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1nox22q.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1nox22q.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1nox22q.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1nox22q.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1nox22q.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1nox22q.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1lfjiu8.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-1lfjiu8.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1lfjiu8.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1lfjiu8.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1lfjiu8.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1lfjiu8 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-1lfjiu8.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1lfjiu8.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-1lfjiu8.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1lfjiu8.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1lfjiu8.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1lfjiu8:after {height: unset;}
	.brz .brz-css-1lfjiu8 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1lfjiu8 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1lfjiu8.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1lfjiu8.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1lfjiu8.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1lfjiu8 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1lfjiu8.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1lfjiu8.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1uq4lhf {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1uq4lhf {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1uq4lhf {position: relative;}
	.brz .brz-css-1uq4lhf:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1uq4lhf {justify-content: flex-start;}}
.brz .brz-css-10cgi2q {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-10cgi2q {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-10cgi2q {justify-content: flex-start;}}
.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-7pqe0i.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-7pqe0i.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-7pqe0i.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-7pqe0i.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-7pqe0i.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-7pqe0i.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-7pqe0i.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-5v297a.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-5v297a.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-5v297a.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-5v297a.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-5v297a.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-5v297a .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-5v297a.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-5v297a.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-5v297a.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-5v297a.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-5v297a.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-5v297a:after {height: unset;}
	.brz .brz-css-5v297a .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-5v297a .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-5v297a.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-5v297a.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-5v297a.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-5v297a .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-5v297a.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-5v297a.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1wim2wv {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1wim2wv {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1wim2wv {position: relative;}
	.brz .brz-css-1wim2wv:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1wim2wv {justify-content: flex-start;}}
.brz .brz-css-177x44y {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-177x44y {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-177x44y {justify-content: flex-start;}}
.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-l98slo.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-l98slo.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-l98slo.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-l98slo.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-l98slo.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-l98slo.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-l98slo.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1aatpbp.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-1aatpbp.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1aatpbp.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1aatpbp.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1aatpbp.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1aatpbp .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-1aatpbp.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1aatpbp.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-1aatpbp.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1aatpbp.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1aatpbp.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1aatpbp:after {height: unset;}
	.brz .brz-css-1aatpbp .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1aatpbp .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1aatpbp.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1aatpbp.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1aatpbp.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1aatpbp .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1aatpbp.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1aatpbp.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-148dahv > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-148dahv {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
	.brz .brz-css-148dahv .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-148dahv > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-148dahv:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-148dahv > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-148dahv > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-148dahv > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-148dahv > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-148dahv:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-qweajh {margin: 0;padding: 16px;}
@media (min-width:991px) {.brz .brz-css-qweajh {z-index: auto;margin: 0;border: 0px solid transparent;padding: 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-qweajh:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-qweajh {padding: 8px;}}
@media (max-width:767px) {.brz .brz-css-qweajh {padding: 25px 0px 0px 0px;}}
.brz .brz-css-1y48u96 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1y48u96 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1y48u96 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1y48u96 {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1y48u96 {margin: 0px 0px 8px 0px;}}
@media (min-width:991px) {.brz .brz-css-tvsjwg {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-bnGyO {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-bnGyO {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bnGyO {font-size: var(--brz-subtitletabletfontsize,initial);line-height: var(--brz-subtitletabletlineheight,initial);font-weight: var(--brz-subtitletabletfontweight,initial);font-weight: var(--brz-subtitletabletbold,initial);letter-spacing: var(--brz-subtitletabletletterspacing,initial);font-variation-settings: var(--brz-subtitletabletfontvariation,initial);font-style: var(--brz-subtitletabletitalic,initial);text-decoration: var(--brz-subtitletablettextdecoration,initial) !important;text-transform: var(--brz-subtitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-bnGyO {text-align: justify !important;font-size: var(--brz-subtitlemobilefontsize,initial);line-height: var(--brz-subtitlemobilelineheight,initial);font-weight: var(--brz-subtitlemobilefontweight,initial);font-weight: var(--brz-subtitlemobilebold,initial);letter-spacing: var(--brz-subtitlemobileletterspacing,initial);font-variation-settings: var(--brz-subtitlemobilefontvariation,initial);font-style: var(--brz-subtitlemobileitalic,initial);text-decoration: var(--brz-subtitlemobiletextdecoration,initial) !important;text-transform: var(--brz-subtitlemobiletexttransform,initial) !important;}}
.brz .brz-css-19rtcdj {margin: 20px 0px 8px -2px;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-19rtcdj {z-index: auto;position: relative;margin: 20px 0px 8px -2px;justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-19rtcdj {position: relative;}
	.brz .brz-css-19rtcdj:hover {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-19rtcdj {margin: 8px 0px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-19rtcdj {margin: 0;}}
.brz .brz-css-mw4u11 {justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-mw4u11 {justify-content: flex-start;padding: 0;gap: 20px 10px;}}
.brz .brz-css-sqbbv8 {font-size: 48px;padding: 0px;border-radius: 0;stroke-width: 1;}
.brz .brz-css-sqbbv8 {color: rgba(var(--brz-global-color3),1);border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}
.brz .brz-css-sqbbv8 .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-sqbbv8 {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-sqbbv8 .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-sqbbv8:hover {color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-sqbbv8:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sqbbv8 {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-sqbbv8 .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:767px) {.brz .brz-css-sqbbv8 {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-sqbbv8 .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-j93mke {font-size: 32px;padding: 14px;border-radius: 100px;}
.brz .brz-css-j93mke {color: rgba(var(--brz-global-color2),1);background-color: rgba(var(--brz-global-color7),.12);}
.brz .brz-css-j93mke .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-j93mke {font-size: 32px;padding: 14px;border-radius: 100px;stroke-width: 1;}
	.brz .brz-css-j93mke:hover {color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(var(--brz-global-color3),1);box-shadow: none;background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-j93mke:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (min-width:991px) {.brz .brz-css-j93mke:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-j93mke:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-j93mke {font-size: 14px;}}
@media (max-width:767px) {.brz .brz-css-j93mke {font-size: 27px;}}
.brz .brz-css-gjsln7 {font-size: 32px;padding: 14px;border-radius: 100px;}
.brz .brz-css-gjsln7 {color: rgba(var(--brz-global-color2),1);background-color: rgba(var(--brz-global-color7),.13);}
.brz .brz-css-gjsln7 .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-gjsln7 {font-size: 32px;padding: 14px;border-radius: 100px;stroke-width: 1;}
	.brz .brz-css-gjsln7:hover {color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(var(--brz-global-color3),1);box-shadow: none;background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-gjsln7:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (min-width:991px) {.brz .brz-css-gjsln7:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-gjsln7:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-gjsln7 {font-size: 14px;}}
@media (max-width:767px) {.brz .brz-css-gjsln7 {font-size: 26px;}}
@media (min-width:991px) {.brz .brz-css-1m3u1b6 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1m3u1b6 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1m3u1b6 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-zwnm8i {height: 10px;}
@media (min-width:991px) {.brz .brz-css-zwnm8i {height: 10px;}}
@media (max-width:767px) {.brz .brz-css-zwnm8i {height: 15px;}}
.brz .brz-css-qxobrp {margin: 0;}
@media (min-width:991px) {.brz .brz-css-qxobrp {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-qxobrp .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-qxobrp {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-lc1q61 {flex-direction: row;}
.brz .brz-css-lc1q61 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}
.brz .brz-css-p80si6 .brz-icon__container {margin-left: auto;margin-right: 20px;}
@media (min-width:991px) {.brz .brz-css-p80si6 {flex-direction: row;}
	.brz .brz-css-p80si6 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}}
@media (max-width:767px) {.brz .brz-css-p80si6 .brz-icon__container {margin-left: auto;margin-right: 12px;}}
.brz .brz-css-1w201we {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-1w201we {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-1w201we:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1w201we:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1w201we:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-10df8lu {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-qLxIb {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-qLxIb {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-qLxIb {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-qLxIb {text-align: justify !important;font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
.brz .brz-css-7xvgik {margin: 2px 0px 8px 0px;}
@media (min-width:991px) {.brz .brz-css-7xvgik {padding: 0;margin: 2px 0px 8px 0px;justify-content: center;position: relative;}
	.brz .brz-css-7xvgik .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-7xvgik {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-7xvgik {margin: 2px 0px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-7xvgik {margin: 2px 0px 0px 0px;}}
.brz .brz-css-33vbr4 .brz-icon__container {margin-left: auto;margin-right: 20px;}
@media (min-width:991px) {.brz .brz-css-33vbr4 {flex-direction: row;}
	.brz .brz-css-33vbr4 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}}
@media (max-width:767px) {.brz .brz-css-33vbr4 .brz-icon__container {margin-left: auto;margin-right: 12px;}}
.brz .brz-css-1hdkxre {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-1hdkxre {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-1hdkxre:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1hdkxre:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1hdkxre:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-mqvrob {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-fMQkB {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-fMQkB {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-fMQkB {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-fMQkB {text-align: justify !important;font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
.brz .brz-css-1yid6jy {margin: 2px 0px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1yid6jy {padding: 0;margin: 2px 0px 0px 0px;justify-content: center;position: relative;}
	.brz .brz-css-1yid6jy .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1yid6jy {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1yid6jy {margin: 8px 0px 0px 0px;}}
@media (min-width:991px) {.brz .brz-css-mpjmry {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-y6prp {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-y6prp {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-y6prp {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-y6prp {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-mbir7l {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-mbir7l .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-mbir7l {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1l3j2t3 {height: 46px;}
@media (min-width:991px) {.brz .brz-css-1l3j2t3 {height: 46px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1l3j2t3 {height: 75px;}}
@media (max-width:767px) {.brz .brz-css-1l3j2t3 {height: 45px;}}
.brz .brz-css-106cr9e {margin: 0;}
@media (min-width:991px) {.brz .brz-css-106cr9e {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-106cr9e .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-106cr9e {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-o4546m {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-gyvoe {margin-top: 0px !important;margin-bottom: 0px !important;text-align: center !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-gyvoe {margin-top: 0px !important;margin-bottom: 0px !important;text-align: center !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-gyvoe {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-gyvoe {font-family: var(--brz-u9xihr8qxhssfontfamily,initial) !important;font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;}}</style><style class="brz-style brz-project__style-palette">.brz .brz-cp-color1, .brz .brz-bcp-color1 {color: rgb(var(--brz-global-color1));}
.brz .brz-bgp-color1 {background-color: rgb(var(--brz-global-color1));}
.brz .brz-cp-color2, .brz .brz-bcp-color2 {color: rgb(var(--brz-global-color2));}
.brz .brz-bgp-color2 {background-color: rgb(var(--brz-global-color2));}
.brz .brz-cp-color3, .brz .brz-bcp-color3 {color: rgb(var(--brz-global-color3));}
.brz .brz-bgp-color3 {background-color: rgb(var(--brz-global-color3));}
.brz .brz-cp-color4, .brz .brz-bcp-color4 {color: rgb(var(--brz-global-color4));}
.brz .brz-bgp-color4 {background-color: rgb(var(--brz-global-color4));}
.brz .brz-cp-color5, .brz .brz-bcp-color5 {color: rgb(var(--brz-global-color5));}
.brz .brz-bgp-color5 {background-color: rgb(var(--brz-global-color5));}
.brz .brz-cp-color6, .brz .brz-bcp-color6 {color: rgb(var(--brz-global-color6));}
.brz .brz-bgp-color6 {background-color: rgb(var(--brz-global-color6));}
.brz .brz-cp-color7, .brz .brz-bcp-color7 {color: rgb(var(--brz-global-color7));}
.brz .brz-bgp-color7 {background-color: rgb(var(--brz-global-color7));}
.brz .brz-cp-color8, .brz .brz-bcp-color8 {color: rgb(var(--brz-global-color8));}
.brz .brz-bgp-color8 {background-color: rgb(var(--brz-global-color8));}
:root {--brz-global-color1: 114,174,172;--brz-global-color2: 50,158,155;--brz-global-color3: 114,174,172;--brz-global-color4: 184,230,225;--brz-global-color5: 84,84,84;--brz-global-color6: 238,243,240;--brz-global-color7: 103,115,108;--brz-global-color8: 255,255,255;}
:root {--brz-paragraphfontfamily: "Comfortaa",display;--brz-paragraphfontsize: 18px;--brz-paragraphfontsizesuffix: px;--brz-paragraphfontweight: 300;--brz-paragraphletterspacing: -.2px;--brz-paragraphlineheight: 1.4;--brz-paragraphfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphtabletfontsize: 17px;--brz-paragraphtabletfontweight: 100;--brz-paragraphtabletletterspacing: 0px;--brz-paragraphtabletlineheight: 1.4;--brz-paragraphtabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphmobilefontsize: 16px;--brz-paragraphmobilefontweight: 100;--brz-paragraphmobileletterspacing: -.2px;--brz-paragraphmobilelineheight: 1.4;--brz-paragraphmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphstoryfontsize: 4.14%;--brz-paragraphbold: 300;--brz-paragraphitalic: inherit;--brz-paragraphtextdecoration: inherit;--brz-paragraphtexttransform: inherit;--brz-paragraphtabletbold: 100;--brz-paragraphtabletitalic: inherit;--brz-paragraphtablettextdecoration: inherit;--brz-paragraphtablettexttransform: inherit;--brz-paragraphmobilebold: 100;--brz-paragraphmobileitalic: inherit;--brz-paragraphmobiletextdecoration: inherit;--brz-paragraphmobiletexttransform: inherit;--brz-subtitlefontfamily: "Comfortaa",display;--brz-subtitlefontsize: 24px;--brz-subtitlefontsizesuffix: px;--brz-subtitlefontweight: 400;--brz-subtitleletterspacing: -.2px;--brz-subtitlelineheight: 1.4;--brz-subtitlefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitletabletfontsize: 20px;--brz-subtitletabletfontweight: 100;--brz-subtitletabletletterspacing: 0px;--brz-subtitletabletlineheight: 1.4;--brz-subtitletabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitlemobilefontsize: 16px;--brz-subtitlemobilefontweight: 100;--brz-subtitlemobileletterspacing: 0px;--brz-subtitlemobilelineheight: 1.4;--brz-subtitlemobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitlestoryfontsize: 5.52%;--brz-subtitlebold: 400;--brz-subtitleitalic: inherit;--brz-subtitletextdecoration: inherit;--brz-subtitletexttransform: inherit;--brz-subtitletabletbold: 100;--brz-subtitletabletitalic: inherit;--brz-subtitletablettextdecoration: inherit;--brz-subtitletablettexttransform: inherit;--brz-subtitlemobilebold: 100;--brz-subtitlemobileitalic: inherit;--brz-subtitlemobiletextdecoration: inherit;--brz-subtitlemobiletexttransform: inherit;--brz-abovetitlefontfamily: "Comfortaa",display;--brz-abovetitlefontsize: 20px;--brz-abovetitlefontsizesuffix: px;--brz-abovetitlefontweight: 400;--brz-abovetitleletterspacing: 0px;--brz-abovetitlelineheight: 1.4;--brz-abovetitlefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitletabletfontsize: 18px;--brz-abovetitletabletfontweight: 400;--brz-abovetitletabletletterspacing: 0px;--brz-abovetitletabletlineheight: 1.4;--brz-abovetitletabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitlemobilefontsize: 18px;--brz-abovetitlemobilefontweight: 400;--brz-abovetitlemobileletterspacing: 0px;--brz-abovetitlemobilelineheight: 1.4;--brz-abovetitlemobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitlestoryfontsize: 4.6%;--brz-abovetitlebold: 400;--brz-abovetitleitalic: inherit;--brz-abovetitletextdecoration: inherit;--brz-abovetitletexttransform: inherit;--brz-abovetitletabletbold: 400;--brz-abovetitletabletitalic: inherit;--brz-abovetitletablettextdecoration: inherit;--brz-abovetitletablettexttransform: inherit;--brz-abovetitlemobilebold: 400;--brz-abovetitlemobileitalic: inherit;--brz-abovetitlemobiletextdecoration: inherit;--brz-abovetitlemobiletexttransform: inherit;--brz-heading1fontfamily: "Comfortaa",display;--brz-heading1fontsize: 54px;--brz-heading1fontsizesuffix: px;--brz-heading1fontweight: 700;--brz-heading1letterspacing: -.2px;--brz-heading1lineheight: 1.2;--brz-heading1fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1tabletfontsize: 40px;--brz-heading1tabletfontweight: 700;--brz-heading1tabletletterspacing: -.2px;--brz-heading1tabletlineheight: 1.2;--brz-heading1tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1mobilefontsize: 28px;--brz-heading1mobilefontweight: 700;--brz-heading1mobileletterspacing: -.8px;--brz-heading1mobilelineheight: 1.2;--brz-heading1mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1storyfontsize: 12.42%;--brz-heading1bold: 700;--brz-heading1italic: inherit;--brz-heading1textdecoration: inherit;--brz-heading1texttransform: inherit;--brz-heading1tabletbold: 700;--brz-heading1tabletitalic: inherit;--brz-heading1tablettextdecoration: inherit;--brz-heading1tablettexttransform: inherit;--brz-heading1mobilebold: 700;--brz-heading1mobileitalic: inherit;--brz-heading1mobiletextdecoration: inherit;--brz-heading1mobiletexttransform: inherit;--brz-heading2fontfamily: "Comfortaa",display;--brz-heading2fontsize: 36px;--brz-heading2fontsizesuffix: px;--brz-heading2fontweight: 700;--brz-heading2letterspacing: -.8px;--brz-heading2lineheight: 1.2;--brz-heading2fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2tabletfontsize: 34px;--brz-heading2tabletfontweight: 700;--brz-heading2tabletletterspacing: -.8px;--brz-heading2tabletlineheight: 1.2;--brz-heading2tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2mobilefontsize: 22px;--brz-heading2mobilefontweight: 700;--brz-heading2mobileletterspacing: -1px;--brz-heading2mobilelineheight: 1.2;--brz-heading2mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2storyfontsize: 8.28%;--brz-heading2bold: 700;--brz-heading2italic: inherit;--brz-heading2textdecoration: inherit;--brz-heading2texttransform: inherit;--brz-heading2tabletbold: 700;--brz-heading2tabletitalic: inherit;--brz-heading2tablettextdecoration: inherit;--brz-heading2tablettexttransform: inherit;--brz-heading2mobilebold: 700;--brz-heading2mobileitalic: inherit;--brz-heading2mobiletextdecoration: inherit;--brz-heading2mobiletexttransform: inherit;--brz-heading3fontfamily: "Comfortaa",display;--brz-heading3fontsize: 22px;--brz-heading3fontsizesuffix: px;--brz-heading3fontweight: 400;--brz-heading3letterspacing: -.6px;--brz-heading3lineheight: 1.4;--brz-heading3fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3tabletfontsize: 28px;--brz-heading3tabletfontweight: 200;--brz-heading3tabletletterspacing: -.2px;--brz-heading3tabletlineheight: 1.3;--brz-heading3tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3mobilefontsize: 20px;--brz-heading3mobilefontweight: 200;--brz-heading3mobileletterspacing: -.8px;--brz-heading3mobilelineheight: 1.3;--brz-heading3mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3storyfontsize: 5.06%;--brz-heading3bold: 400;--brz-heading3italic: inherit;--brz-heading3textdecoration: inherit;--brz-heading3texttransform: inherit;--brz-heading3tabletbold: 200;--brz-heading3tabletitalic: inherit;--brz-heading3tablettextdecoration: inherit;--brz-heading3tablettexttransform: inherit;--brz-heading3mobilebold: 200;--brz-heading3mobileitalic: inherit;--brz-heading3mobiletextdecoration: inherit;--brz-heading3mobiletexttransform: inherit;--brz-heading4fontfamily: "Comfortaa",display;--brz-heading4fontsize: 18px;--brz-heading4fontsizesuffix: px;--brz-heading4fontweight: 700;--brz-heading4letterspacing: -.2px;--brz-heading4lineheight: 1.4;--brz-heading4fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4tabletfontsize: 17px;--brz-heading4tabletfontweight: 700;--brz-heading4tabletletterspacing: 0px;--brz-heading4tabletlineheight: 1.4;--brz-heading4tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4mobilefontsize: 17px;--brz-heading4mobilefontweight: 700;--brz-heading4mobileletterspacing: 0px;--brz-heading4mobilelineheight: 1.4;--brz-heading4mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4storyfontsize: 4.14%;--brz-heading4bold: 700;--brz-heading4italic: inherit;--brz-heading4textdecoration: inherit;--brz-heading4texttransform: inherit;--brz-heading4tabletbold: 700;--brz-heading4tabletitalic: inherit;--brz-heading4tablettextdecoration: inherit;--brz-heading4tablettexttransform: inherit;--brz-heading4mobilebold: 700;--brz-heading4mobileitalic: inherit;--brz-heading4mobiletextdecoration: inherit;--brz-heading4mobiletexttransform: inherit;--brz-heading5fontfamily: "Comfortaa",display;--brz-heading5fontsize: 16px;--brz-heading5fontsizesuffix: px;--brz-heading5fontweight: 400;--brz-heading5letterspacing: 0px;--brz-heading5lineheight: 1.4;--brz-heading5fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5tabletfontsize: 16px;--brz-heading5tabletfontweight: 400;--brz-heading5tabletletterspacing: 0px;--brz-heading5tabletlineheight: 1.4;--brz-heading5tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5mobilefontsize: 16px;--brz-heading5mobilefontweight: 400;--brz-heading5mobileletterspacing: 0px;--brz-heading5mobilelineheight: 1.4;--brz-heading5mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5storyfontsize: 3.68%;--brz-heading5bold: 400;--brz-heading5italic: inherit;--brz-heading5textdecoration: inherit;--brz-heading5texttransform: inherit;--brz-heading5tabletbold: 400;--brz-heading5tabletitalic: inherit;--brz-heading5tablettextdecoration: inherit;--brz-heading5tablettexttransform: inherit;--brz-heading5mobilebold: 400;--brz-heading5mobileitalic: inherit;--brz-heading5mobiletextdecoration: inherit;--brz-heading5mobiletexttransform: inherit;--brz-heading6fontfamily: "Comfortaa",display;--brz-heading6fontsize: 14px;--brz-heading6fontsizesuffix: px;--brz-heading6fontweight: 300;--brz-heading6letterspacing: -.4px;--brz-heading6lineheight: 1.5;--brz-heading6fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6tabletfontsize: 14px;--brz-heading6tabletfontweight: 400;--brz-heading6tabletletterspacing: 0px;--brz-heading6tabletlineheight: 1.5;--brz-heading6tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6mobilefontsize: 14px;--brz-heading6mobilefontweight: 400;--brz-heading6mobileletterspacing: 0px;--brz-heading6mobilelineheight: 1.5;--brz-heading6mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6storyfontsize: 3.22%;--brz-heading6bold: 300;--brz-heading6italic: inherit;--brz-heading6textdecoration: inherit;--brz-heading6texttransform: inherit;--brz-heading6tabletbold: 400;--brz-heading6tabletitalic: inherit;--brz-heading6tablettextdecoration: inherit;--brz-heading6tablettexttransform: inherit;--brz-heading6mobilebold: 400;--brz-heading6mobileitalic: inherit;--brz-heading6mobiletextdecoration: inherit;--brz-heading6mobiletexttransform: inherit;--brz-buttonfontfamily: "Comfortaa",display;--brz-buttonfontsize: 18px;--brz-buttonfontsizesuffix: px;--brz-buttonfontweight: 700;--brz-buttonletterspacing: -.2px;--brz-buttonlineheight: 1.6;--brz-buttonfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttontabletfontsize: 16px;--brz-buttontabletfontweight: 700;--brz-buttontabletletterspacing: 0px;--brz-buttontabletlineheight: 1.6;--brz-buttontabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttonmobilefontsize: 16px;--brz-buttonmobilefontweight: 700;--brz-buttonmobileletterspacing: 0px;--brz-buttonmobilelineheight: 1.6;--brz-buttonmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttonstoryfontsize: 4.14%;--brz-buttonbold: 700;--brz-buttonitalic: inherit;--brz-buttontextdecoration: inherit;--brz-buttontexttransform: inherit;--brz-buttontabletbold: 700;--brz-buttontabletitalic: inherit;--brz-buttontablettextdecoration: inherit;--brz-buttontablettexttransform: inherit;--brz-buttonmobilebold: 700;--brz-buttonmobileitalic: inherit;--brz-buttonmobiletextdecoration: inherit;--brz-buttonmobiletexttransform: inherit;--brz-ugudlcdcxlbqfontfamily: "Comfortaa",display;--brz-ugudlcdcxlbqfontsize: 16px;--brz-ugudlcdcxlbqfontsizesuffix: px;--brz-ugudlcdcxlbqfontweight: 400;--brz-ugudlcdcxlbqletterspacing: -.2px;--brz-ugudlcdcxlbqlineheight: 1.4;--brz-ugudlcdcxlbqfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqtabletfontsize: 15px;--brz-ugudlcdcxlbqtabletfontweight: 400;--brz-ugudlcdcxlbqtabletletterspacing: -.2px;--brz-ugudlcdcxlbqtabletlineheight: 1.5;--brz-ugudlcdcxlbqtabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqmobilefontsize: 15px;--brz-ugudlcdcxlbqmobilefontweight: 400;--brz-ugudlcdcxlbqmobileletterspacing: -.2px;--brz-ugudlcdcxlbqmobilelineheight: 1.4;--brz-ugudlcdcxlbqmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqstoryfontsize: 3.68%;--brz-ugudlcdcxlbqbold: 400;--brz-ugudlcdcxlbqitalic: inherit;--brz-ugudlcdcxlbqtextdecoration: inherit;--brz-ugudlcdcxlbqtexttransform: inherit;--brz-ugudlcdcxlbqtabletbold: 400;--brz-ugudlcdcxlbqtabletitalic: inherit;--brz-ugudlcdcxlbqtablettextdecoration: inherit;--brz-ugudlcdcxlbqtablettexttransform: inherit;--brz-ugudlcdcxlbqmobilebold: 400;--brz-ugudlcdcxlbqmobileitalic: inherit;--brz-ugudlcdcxlbqmobiletextdecoration: inherit;--brz-ugudlcdcxlbqmobiletexttransform: inherit;--brz-u9xihr8qxhssfontfamily: "Comfortaa",display;--brz-u9xihr8qxhssfontsize: 14px;--brz-u9xihr8qxhssfontsizesuffix: px;--brz-u9xihr8qxhssfontweight: 700;--brz-u9xihr8qxhssletterspacing: -.2px;--brz-u9xihr8qxhsslineheight: 1.4;--brz-u9xihr8qxhssfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhsstabletfontsize: 14px;--brz-u9xihr8qxhsstabletfontweight: 400;--brz-u9xihr8qxhsstabletletterspacing: -.3px;--brz-u9xihr8qxhsstabletlineheight: 1.2;--brz-u9xihr8qxhsstabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhssmobilefontsize: 13px;--brz-u9xihr8qxhssmobilefontweight: 700;--brz-u9xihr8qxhssmobileletterspacing: -.4px;--brz-u9xihr8qxhssmobilelineheight: 1.3;--brz-u9xihr8qxhssmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhssstoryfontsize: 3.22%;--brz-u9xihr8qxhssbold: 700;--brz-u9xihr8qxhssitalic: inherit;--brz-u9xihr8qxhsstextdecoration: inherit;--brz-u9xihr8qxhsstexttransform: inherit;--brz-u9xihr8qxhsstabletbold: 400;--brz-u9xihr8qxhsstabletitalic: inherit;--brz-u9xihr8qxhsstablettextdecoration: inherit;--brz-u9xihr8qxhsstablettexttransform: inherit;--brz-u9xihr8qxhssmobilebold: 700;--brz-u9xihr8qxhssmobileitalic: inherit;--brz-u9xihr8qxhssmobiletextdecoration: inherit;--brz-u9xihr8qxhssmobiletexttransform: inherit;}</style><script src="../assets/1388456933bb241416bad971c145e501.js" class="brz-script brz-script-preview-lib" defer="true" data-brz-group="group-1"></script><script src="../assets/7bd5bb61c3a6cf37aef2a8ce02be7461.js" class="brz-script brz-script-preview-lib-pro" defer="true" data-brz-group="group-1_2"></script><script src="../assets/246b62aec7f6f35cef79e957ee00b9c9.js" class="brz-script brz-script-preview-pro" defer="true"></script></head><body class="brz">        <div class="brz-root__container brz-reset-all brz brz-root__container-page"><section id="i1d1d461f2ee3ec5ba41c_add3583050b76c25118a9b567f3238c29" class="brz-section brz-section__header brz-css-sk5zf3 brz-css-c8fq4o"><div class="brz-section__menu-item brz-css-1wzn4p1 brz-css-1uopts9" data-brz-custom-id="pmjtztsvmmgucjewowvarmptbegnhqkrygyg"><div class="brz-container brz-css-2gpbzd brz-css-1efqzy4"><header class="brz-row__container brz-css-1t66ezt brz-css-1a7ogzm" data-brz-custom-id="qxrqcghhpyzdntkwxqtwalcnjpphwfwfpnai"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-row brz-css-lt4404 brz-css-18oneyz"><div class="brz-columns brz-css-1csnpdv brz-css-88dicl" data-brz-custom-id="mxbfnikhdyxqeblxvgyysleuexbinafgdwmy"><div class="brz-column__items brz-css-1pzte4r brz-css-znagm0"><div id="" class="brz-css-1tjbyob brz-css-1puqgto brz-wrapper"><div class="brz-image brz-css-q31jzm brz-css-yp9xav" data-brz-custom-id="ozphomskbadqfdvikpfdqyfniixzvkbtxcbk"><a class="brz-a" href="/" target="_self" rel="noopener" data-brz-link-type="external"><picture class="brz-picture brz-d-block brz-p-relative brz-css-1yykq65 brz-css-z4zlrs"><source srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" media="(min-width: 992px)"><source srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" media="(min-width: 768px)"><img class="brz-img" srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" src="../assets/5bd10bf04072b5d66d2acac718fe483e.webp" alt="" title="cymta-logo-el.webp" draggable="false" loading="lazy"></picture></a></div></div></div></div><div class="brz-columns brz-css-1csnpdv brz-css-1t2tmqk" data-brz-custom-id="lvqsghryjlhtilcubdlpnhcdhvaftslruozx"><div class="brz-column__items brz-css-1pzte4r brz-css-kmg040"><div id="" class="brz-css-1tjbyob brz-css-icg1tg brz-wrapper"><div class="brz-menu__container brz-css-19uzdaw brz-css-159oqwi" data-mmenu-id="#fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw_icf78dcbb587810109807" data-mmenu-position="position-left" data-mmenu-title="Menu" data-mmenu-stickytitle="on" data-mmenu-closingicon="%7B%22desktop%22%3A%22off%22%2C%22tablet%22%3A%22off%22%2C%22mobile%22%3A%22off%22%7D" data-brz-custom-id="fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw"><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" class="brz-menu brz-menu__preview brz-css-c39cel brz-css-1tdvdmo"><ul class="brz-menu__ul"><li data-menu-item-id="b22b960322341a5e734d1324bbd76ed2" class="brz-menu__item"><a class="brz-a" target="" href="/mt" title="Μουσικοθεραπεία"><span class="brz-span">Μουσικοθεραπεία</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="01d48bae25f7229912cdc6bd654489a6" class="brz-menu__item"><a class="brz-a" target="" href="/find" title="Βρες Μουσικοθεραπευτή"><span class="brz-span">Βρες Μουσικοθεραπευτή</span></a></li><li data-menu-item-id="ead41e4a9b5e03ba07aeb9e426f4c21a" class="brz-menu__item"><a class="brz-a" target="" href="/study" title="Σπουδές Μουσικοθεραπείας"><span class="brz-span">Σπουδές Μουσικοθεραπείας</span></a></li><li data-menu-item-id="602acc623566a539fa600e1a24add4a0" class="brz-menu__item"><a class="brz-a" target="" href="/mt-info" title="Πληροφορίες για Μουσικοθεραπευτές"><span class="brz-span">Πληροφορίες για Μουσικοθεραπευτές</span></a></li></ul></li><li data-menu-item-id="3e349ad76a29de800ba11951eee69b60" class="brz-menu__item"><a class="brz-a" target="" href="/mt-blog" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="ccf214e2ed986735e372c15ea2c09523" class="brz-menu__item brz-menu__item--current"><a class="brz-a" target="" href="/contact" title="Επικοινωνία"><span class="brz-span">Επικοινωνία</span></a></li></ul></nav><div class="brz-mm-menu__icon"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/8e85539e41ed2686da0dcff2d601af13.svg#brz_icon"></use></svg></div><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" id="fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw_icf78dcbb587810109807" class="brz-menu brz-menu__preview brz-menu__mmenu brz-menu--has-dropdown brz-css-12dn42o brz-css-1x5bkh9"><ul class="brz-menu__ul"><li data-menu-item-id="b22b960322341a5e734d1324bbd76ed2" class="brz-menu__item"><a class="brz-a" target="" href="/mt" title="Μουσικοθεραπεία"><span class="brz-span">Μουσικοθεραπεία</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="01d48bae25f7229912cdc6bd654489a6" class="brz-menu__item"><a class="brz-a" target="" href="/find" title="Βρες Μουσικοθεραπευτή"><span class="brz-span">Βρες Μουσικοθεραπευτή</span></a></li><li data-menu-item-id="ead41e4a9b5e03ba07aeb9e426f4c21a" class="brz-menu__item"><a class="brz-a" target="" href="/study" title="Σπουδές Μουσικοθεραπείας"><span class="brz-span">Σπουδές Μουσικοθεραπείας</span></a></li><li data-menu-item-id="602acc623566a539fa600e1a24add4a0" class="brz-menu__item"><a class="brz-a" target="" href="/mt-info" title="Πληροφορίες για Μουσικοθεραπευτές"><span class="brz-span">Πληροφορίες για Μουσικοθεραπευτές</span></a></li></ul></li><li data-menu-item-id="3e349ad76a29de800ba11951eee69b60" class="brz-menu__item"><a class="brz-a" target="" href="/mt-blog" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="ccf214e2ed986735e372c15ea2c09523" class="brz-menu__item brz-menu__item--current"><a class="brz-a" target="" href="/contact" title="Επικοινωνία"><span class="brz-span">Επικοινωνία</span></a></li></ul></nav></div></div><div id="" class="brz-css-1tjbyob brz-css-19ioiov brz-wrapper"><div class="brz-menu__container brz-css-19uzdaw brz-css-1mxc0dk" data-mmenu-id="#tkCzeDJsTpP8_icf78dcbb587810109807" data-mmenu-position="position-left" data-mmenu-title="" data-mmenu-stickytitle="on" data-mmenu-closingicon="%7B%22desktop%22%3A%22off%22%2C%22tablet%22%3A%22off%22%2C%22mobile%22%3A%22off%22%7D" data-brz-custom-id="tkCzeDJsTpP8"><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" class="brz-menu brz-menu__preview brz-css-c39cel brz-css-1dgf3el"><ul class="brz-menu__ul"><li data-menu-item-id="68498357bd9751cca09ce585b751e087" class="brz-menu__item"><a class="brz-a" target="" href="/mt" title="Μουσικοθεραπεία"><span class="brz-span">Μουσικοθεραπεία</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="75d4d1dc5b0c65580e43666ccee4f7a7" class="brz-menu__item"><a class="brz-a" target="" href="/find" title="Βρες Μουσικοθεραπευτή"><span class="brz-span">Βρες Μουσικοθεραπευτή</span></a></li><li data-menu-item-id="8b426b54feb315f34ab6402a783f37ea" class="brz-menu__item"><a class="brz-a" target="" href="/study" title="Σπουδές Μουσικοθεραπείας"><span class="brz-span">Σπουδές Μουσικοθεραπείας</span></a></li><li data-menu-item-id="079c12ac444e6db44d0c1aef67233f3b" class="brz-menu__item"><a class="brz-a" target="" href="/mt-info" title="Πληροφορίες για Μουσικοθεραπευτές"><span class="brz-span">Πληροφορίες για Μουσικοθεραπευτές</span></a></li></ul></li><li data-menu-item-id="54aaa9e0763f70d42976f10caca42b9e" class="brz-menu__item"><a class="brz-a" target="" href="/mt-blog" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="8db2f3adb442538b322f3a1e9822fb29" class="brz-menu__item brz-menu__item--current"><a class="brz-a" target="" href="/contact" title="Επικοινωνία"><span class="brz-span">Επικοινωνία</span></a></li><li data-menu-item-id="0c5b64c5539a611a193d2ef63578b507" class="brz-menu__item"><a class="brz-a" target="" href="/en" title="EN"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/83309393473f0d678e4bb3660c2ccfea.svg#nc_icon"></use></svg><span class="brz-span">EN</span></a></li></ul></nav><div class="brz-mm-menu__icon"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/8e85539e41ed2686da0dcff2d601af13.svg#brz_icon"></use></svg></div><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" id="tkCzeDJsTpP8_icf78dcbb587810109807" class="brz-menu brz-menu__preview brz-menu__mmenu brz-menu--has-dropdown brz-css-12dn42o brz-css-1kwae1d"><ul class="brz-menu__ul"><li data-menu-item-id="68498357bd9751cca09ce585b751e087" class="brz-menu__item"><a class="brz-a" target="" href="/mt" title="Μουσικοθεραπεία"><span class="brz-span">Μουσικοθεραπεία</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="75d4d1dc5b0c65580e43666ccee4f7a7" class="brz-menu__item"><a class="brz-a" target="" href="/find" title="Βρες Μουσικοθεραπευτή"><span class="brz-span">Βρες Μουσικοθεραπευτή</span></a></li><li data-menu-item-id="8b426b54feb315f34ab6402a783f37ea" class="brz-menu__item"><a class="brz-a" target="" href="/study" title="Σπουδές Μουσικοθεραπείας"><span class="brz-span">Σπουδές Μουσικοθεραπείας</span></a></li><li data-menu-item-id="079c12ac444e6db44d0c1aef67233f3b" class="brz-menu__item"><a class="brz-a" target="" href="/mt-info" title="Πληροφορίες για Μουσικοθεραπευτές"><span class="brz-span">Πληροφορίες για Μουσικοθεραπευτές</span></a></li></ul></li><li data-menu-item-id="54aaa9e0763f70d42976f10caca42b9e" class="brz-menu__item"><a class="brz-a" target="" href="/mt-blog" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="8db2f3adb442538b322f3a1e9822fb29" class="brz-menu__item brz-menu__item--current"><a class="brz-a" target="" href="/contact" title="Επικοινωνία"><span class="brz-span">Επικοινωνία</span></a></li><li data-menu-item-id="0c5b64c5539a611a193d2ef63578b507" class="brz-menu__item"><a class="brz-a" target="" href="/en" title="EN"><svg class="brz-icon-svg align-[initial] brz-mm-menu__item__icon"><use href="../assets/svg/83309393473f0d678e4bb3660c2ccfea.svg#nc_icon"></use></svg><span class="brz-span">EN</span></a></li></ul></nav></div></div></div></div><div id="translatorclick" class="brz-columns brz-css-1csnpdv brz-css-jow5dd" data-brz-custom-id="ofxnnpp8wVND"><div class="brz-column__items brz-css-1pzte4r brz-css-190lc1z"><div id="" class="brz-css-1tjbyob brz-css-xmdiqp brz-wrapper"><div class="brz-wrapper-transform"><div class="brz-icon-text brz-css-nntapz brz-css-1umndxb" data-brz-custom-id="gsFk4ZWbjT9L"><div class="brz-icon__container" data-brz-custom-id="oNVhNf89QSOh"><span class="brz-icon brz-span brz-css-e9bk1k brz-css-h3q0h5"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/673cf948d20838cde5e7c8f64474298f.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-10fnxcx brz-css-1n6htfm" data-brz-custom-id="k7MTYkg143MN"><div data-brz-translate-text="1"><p class="brz-mt-lg-0 brz-text-lg-justify brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-empty brz-ff-comfortaa brz-ft-google brz-fs-lg-16 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-m_0_2 brz-lh-lg-1_7 brz-css-of8RQ" data-uniq-id="yAzBt" data-generated-css="brz-css-wO7DX"><span style="color: rgba(var(--brz-global-color7),1);" class="brz-cp-color7">EN</span></p></div></div></div></div></div></div><div id="" class="brz-css-1tjbyob brz-css-r8i1ey brz-wrapper"><div class="brz-embed-code brz-css-zwba1n brz-css-3bcg12" data-brz-custom-id="dkIB9yIrtPL_"><div class="brz-embed-content"><div><style>#translatorclick {transition: transform .3s ease-in-out;}
#translatorclick:hover {transform: scale(1.08);}</style></div></div></div></div></div><a class="brz-a brz-container-link" href="/en" target="_self" rel="noopener" data-brz-link-type="external"></a></div></div></header></div></div></section>     <section id="i77baa9501c76c3a0435d_dwrzzczjcvpjotalocoihvpnqqdsfpqqzyjp" class="brz-section brz-css-1tbl67r brz-css-ks7vcw"><div class="brz-section__content brz-section--boxed brz-css-1ibzn60 brz-css-6g79fi" data-brz-custom-id="scjuknbapppahflbdqwkfhnycmfrrzbpmsvh"><div class="brz-bg"><div class="brz-bg-image"></div><div class="brz-bg-color"></div></div><div class="brz-container brz-css-1eg5ds5 brz-css-54tm0n"><div id="" class="brz-css-1gxjaun brz-css-1gh8ats brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-lxk38o brz-css-pasmh5" data-brz-custom-id="h93q7opLDYYg"><div data-brz-translate-text="1"><h1 data-uniq-id="eTHaR" data-generated-css="brz-css-klDG3" class="brz-tp-lg-heading1 brz-css-zfjee"><span style="color: rgba(var(--brz-global-color8),1); text-shadow: rgb(0, 0, 0) 1px 1px 4px;" class="brz-cp-color8">Επικοινωνία</span></h1></div></div></div><div id="" class="brz-css-1gxjaun brz-css-1tk619v brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-lxk38o brz-css-1y7w3xf" data-brz-custom-id="dX5MzQEvzlWX"><div data-brz-translate-text="1"><p class="brz-tp-lg-subtitle brz-css-m3l_8" data-generated-css="brz-css-m7Cps" data-uniq-id="czpxb"><span class="brz-cp-color8" style="text-shadow: rgb(0, 0, 0) 1px 1px 3px; color: rgba(var(--brz-global-color8),1);">Σύλλογος Εγγεγραμμένων Μουσικοθεραπευτών Κύπρου</span></p></div></div></div></div></div></section><section id="i4703f0d42d5fea0c1fc2_ozvtvwqiwkitmpndopamqpellmsclpemhycn" class="brz-section brz-css-1tbl67r brz-css-1scz54i"><div class="brz-section__content brz-section--boxed brz-css-1ibzn60 brz-css-10twr81" data-brz-custom-id="qnomayprglcoqugmrnqcdrekkvkuvcjjfeim"><div class="brz-bg"><div class="brz-bg-image"></div><div class="brz-bg-color"></div></div><div class="brz-container brz-css-1eg5ds5 brz-css-koybsp"><div class="brz-row__container brz-css-1veody8 brz-css-1th7qlv" data-brz-custom-id="pwhhnsejcvtyqulnliilfjqojmgixhrabhuf"><div class="brz-row brz-css-1f0zir6 brz-css-181mqo8 brz-css-tz6ee8"><div class="brz-columns brz-css-ui298x brz-css-1luw88h" data-brz-custom-id="bqhzlqlbcehzldfmxzhnptloldvwleqmgzzr"><div class="brz-column__items brz-css-cbzve2 brz-css-1pc7pji"><div id="" class="brz-css-1gxjaun brz-css-ms6n28 brz-css-1ohaa5w brz-wrapper"><div class="brz-spacer brz-css-1aho7cw brz-css-ga6b1k"></div></div><div id="" class="brz-css-1gxjaun brz-css-1o759s8 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-lxk38o brz-css-1msabsm" data-brz-custom-id="yhpjukujorevfjabyzrvbdormquhdidpxgkg"><div data-brz-translate-text="1"><h2 data-generated-css="brz-css-wc9Qq" data-uniq-id="jKxWM" class="brz-fs-lg-44 brz-ft-google brz-ff-lato brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_2 brz-ls-lg-0 brz-fw-lg-700 brz-fss-lg-px brz-tp-lg-heading2 brz-css-okatz"><span class="brz-cp-color2">Επικοινωνήστε μαζί μας</span></h2></div></div></div><div id="" class="brz-css-1gxjaun brz-css-ms6n28 brz-css-vvbv0v brz-wrapper"><div class="brz-spacer brz-css-1aho7cw brz-css-xcrd1l"></div></div><div id="" class="brz-css-1gxjaun brz-css-4yi92m brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-lxk38o brz-css-1wvksj6" data-brz-custom-id="nxxfzxeesykiwtpudgeflvennfqmiydnlwhq"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_5 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-18 brz-ft-google brz-ff-lato brz-tp-lg-paragraph brz-css-eVSNG" data-uniq-id="xth09" data-generated-css="brz-css-n0nmM"><span class="brz-cp-color7">Έχετε απορίες ή ερωτήσεις σχετικά με τη μουσικοθεραπεία; Επικοινωνήστε μαζί μας χρησιμοποιώντας την παρακάτω φόρμα.</span></p></div></div></div><div id="" class="brz-css-1gxjaun brz-css-ms6n28 brz-css-na1cz1 brz-wrapper"><div class="brz-spacer brz-css-1aho7cw brz-css-nqqubv"></div></div><div id="" class="brz-css-1gxjaun brz-css-kh4pcm brz-wrapper"><div data-brz-form-version="2" class="brz-forms2 brz-css-j299c5 brz-css-1gagtz2" data-brz-custom-id="a2fb2b046184273b63d9ce02f071f685a"><form class="brz-form brz-d-xs-flex brz-flex-xs-wrap" novalidate="" action="https://msg.formsender.online/form/submit" data-brz-form-id="a2fb2b046184273b63d9ce02f071f685a" data-brz-project-id="22459060" data-brz-success="" data-brz-error="" data-brz-redirect="" data-brz-form-type="default" data-brz-translatable-default-success="Your email was sent successfully" data-brz-translatable-default-error="Your email was not sent" data-brz-translatable-default-empty="Please check your entry and try again"><div data-brz-translate-text="1" class="brz-forms2__item brz-css-1g9uile brz-css-du33fw brz-css-udh86i brz-css-3xinmt"><input type="text" id="vXyGsSzQ6oSa_ie8de893a897d87c8fb5f" name="gnxgzwmeevcelopjmtycohqxkrxzipgypxvv" placeholder="Όνομα" required="" data-brz-type="Text" data-brz-label="Όνομα" data-brz-placeholder="Όνομα" class="brz-input brz-forms2__field brz-forms2__field-text"></div><div data-brz-translate-text="1" class="brz-forms2__item brz-css-1g9uile brz-css-du33fw brz-css-udh86i brz-css-1v1bre7"><input type="email" id="dyYIVmx0TPED_i5dcc2cb855c58c2e1035" name="phxrnweivbkvdxmglvqcclrjloiolvaorxes" placeholder="Email Address" required="" pattern="%5E((%5B%5E%3C%3E%5C(%5C)%5C%5B%5C%5D%5C%5C.,;:%5Cs@%22%5D+(%5C.%5B%5E%3C%3E%5C(%5C)%5C%5B%5C%5D%5C%5C.,;:%5Cs@%22%5D+)*)%7C(%22.+%22))@((%5C%5B%5B0-9%5D%7B1,3%7D%5C.%5B0-9%5D%7B1,3%7D%5C.%5B0-9%5D%7B1,3%7D%5C.%5B0-9%5D%7B1,3%7D%5C%5D)%7C((%5Ba-zA-Z%5C-0-9%5D+%5C.)+%5Ba-zA-Z%5D%7B2,%7D))$" data-brz-type="Email" data-brz-label="Email Address" data-brz-placeholder="Email Address" maxlength="255" class="brz-input brz-forms2__field brz-forms2__field-email"></div><div data-brz-translate-text="1" class="brz-forms2__item brz-css-1g9uile brz-css-du33fw brz-css-udh86i brz-css-10297kk"><input type="text" id="xAaPYvw6XjUM_ic8b3cd69e55bfc850b16" name="rklrwxhoelvjgcstlnrhwboqxjpwjjuygbdc" placeholder="Επίθετο" required="" data-brz-type="Text" data-brz-label="Επίθετο" data-brz-placeholder="Επίθετο" class="brz-input brz-forms2__field brz-forms2__field-text"></div><div data-brz-translate-text="1" class="brz-forms2__item brz-css-1g9uile brz-css-du33fw brz-css-udh86i brz-css-azhk2l"><input type="text" id="hzugd_dmzd7K_i8d9af744ad3704b62b9d" name="diqaajypiietxxdaqfmrymwpozgbnauuubwo" placeholder="Θέμα" required="" data-brz-type="Text" data-brz-label="Θέμα" data-brz-placeholder="Θέμα" class="brz-input brz-forms2__field brz-forms2__field-text"></div><div data-brz-translate-text="1" class="brz-forms2__item brz-css-1g9uile brz-css-du33fw brz-d-none brz-css-udh86i brz-css-k82x81"><input type="hidden" id="g5PVgqsZjsrY_ibde2f6e3c28a61d59bbe" name="uqjLicFlIT0b" placeholder="CYMTA Website" data-brz-type="Hidden" data-brz-label="CYMTA Website" data-brz-placeholder="CYMTA Website" class="brz-input brz-forms2__field brz-forms2__field-hidden"></div><div data-brz-translate-text="1" class="brz-forms2__item brz-css-1g9uile brz-css-du33fw brz-css-udh86i brz-css-c1e7i6"><textarea id="eaj46E7t6_mC_i27efccb0fcf5a214d047" name="yijogxgulhunhxmwacbxgyhbpveqlzywfupb" placeholder="Μήνυμα" required="" data-brz-type="Paragraph" data-brz-label="Μήνυμα" data-brz-placeholder="Μήνυμα" class="brz-textarea brz-forms2__field brz-forms2__field-paragraph"></textarea></div><div class="brz-forms2 brz-forms2__item brz-forms2__item-button" data-brz-custom-id="a2fb2b046184273b63d9ce02f071f685a"><button class="brz-btn brz-btn-submit brz-css-i4nkhk brz-css-1np728w" data-brz-custom-id="hqqhwvbblyavwgzgbmagnmlicrspodztrnjp"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Αποστολή</span></button><svg class="brz-icon-svg align-[initial] brz-form-spinner brz-invisible brz-ed-animated--spin"><use href="../assets/svg/bd922d391dbe0ef2619a27dc1c32f252.svg#nc_icon"></use></svg></div><div class="brz-g-recaptcha" data-sitekey="" data-size="invisible" data-callback="brzFormV2Captcha"></div></form></div></div></div></div><div class="brz-columns brz-css-ui298x brz-css-1qt9xmy" data-brz-custom-id="ramwivfxgxfckgrtrjxarzsaffywcicbfqiy"><div class="brz-column__items brz-css-cbzve2 brz-css-1pfde6n"><div class="brz-row__container brz-css-1veody8 brz-css-144sj08" data-brz-custom-id="auwserfavejjuqxczllupbnfkkfqnrfhrwwr"><div class="brz-row brz-row--inner brz-css-1f0zir6 brz-css-181mqo8 brz-css-13q9k97"><div class="brz-columns brz-css-ui298x brz-css-193bbju" data-brz-custom-id="gsyjyyksorxhwaerschmeicmqmxsilxyhoyy"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-column__items brz-css-cbzve2 brz-css-lhglmv"><div id="" class="brz-css-1gxjaun brz-css-1m6o2vs brz-wrapper"><div class="brz-image brz-css-1eu5ryl brz-css-1oppknj" data-brz-custom-id="uoxKZPdgL7nL"><picture class="brz-picture brz-d-block brz-p-relative brz-css-448k3s brz-css-uii6g2"><source srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" media="(min-width: 992px)"><source srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" media="(min-width: 768px)"><img class="brz-img" srcset="../assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, ../assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" src="../assets/5bd10bf04072b5d66d2acac718fe483e.webp" alt="" title="cymta-logo-el.webp" draggable="false" loading="lazy"></picture></div></div><div id="" class="brz-css-1gxjaun brz-css-ms6n28 brz-css-sn76wk brz-wrapper"><div class="brz-spacer brz-css-1aho7cw brz-css-10fdezb"></div></div><div id="" class="brz-css-1gxjaun brz-css-1cuti6p brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-lxk38o brz-css-msar9s" data-brz-custom-id="swukkblnhiymhjimuumjrpoitdhzvyebdnpy"><div data-brz-translate-text="1"><p class="brz-tp-lg-abovetitle brz-css-zZ04J" data-generated-css="brz-css-cMq7p" data-uniq-id="fhjg3"><span class="brz-cp-color8">+357 97 661501</span></p><p class="brz-tp-lg-abovetitle brz-lh-lg-1_2 brz-ls-lg-0 brz-fw-lg-700 brz-fss-lg-px brz-fs-lg-32 brz-ft-google brz-ff-lato brz-css-lJ9Ib" data-uniq-id="a5DZp" data-generated-css="brz-css-xtIcD"><span class="brz-cp-color8"><EMAIL></span></p></div></div></div><div id="" class="brz-css-1gxjaun brz-css-ms6n28 brz-css-1m137ye brz-wrapper"><div class="brz-spacer brz-css-1aho7cw brz-css-t567n6"></div></div><div id="" class="brz-css-1gxjaun brz-css-1diwqgn brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-lxk38o brz-css-1hwd4tc" data-brz-custom-id="wjmuilhonpisaqhmtkeeynmqvnoiumwfnuzk"><div data-brz-translate-text="1"><p class="brz-tp-lg-abovetitle brz-css-dB38u" data-uniq-id="r8hrk" data-generated-css="brz-css-hYW4X"><span class="brz-cp-color8">Οινόης 7, Στρόβολος 2037 Λευκωσία</span></p></div></div></div><div id="" class="brz-css-1gxjaun brz-css-ms6n28 brz-css-1tofl41 brz-wrapper"><div class="brz-spacer brz-css-1aho7cw brz-css-1g8p7s5"></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-9s4nrj brz-css-mnqhml" data-brz-custom-id="yudadhuhighhkihjocrvooihgdecrawesxcz"><div class="brz-icon__container" data-brz-custom-id="mvouqaapchdvcrpitbdxwvcojdtbveebslcx"><a class="brz-a" href="https://www.facebook.com/profile.php?id=61572078845247" target="_self" rel="noopener" data-brz-link-type="external"><span class="brz-icon brz-span brz-css-1cdczpf brz-css-2mv7o5"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/d710c5ce3ce8671b1521487d79654e3d.svg#nc_icon"></use></svg></span></a></div><div class="brz-icon__container" data-brz-custom-id="yoavibgdxdxutwggdljdkxmrpezvukxamxjm"><a class="brz-a" href="https://www.instagram.com/music_therapy_cy" target="_self" rel="noopener" data-brz-link-type="external"><span class="brz-icon brz-span brz-css-1cdczpf brz-css-1u2geql"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/207d5e57bd91d810599bb7b29b33c358.svg#nc_icon"></use></svg></span></a></div></div></div></div></div></div></div></div></div></div></div></div></section><section id="footer" class="brz-section brz-css-2sdt19 brz-css-1ij56rs"><div class="brz-section__content brz-section--boxed brz-css-guytnn brz-css-1xl9s1b" data-brz-custom-id="wqruexykkmfstbrfmiurpgxhnsclaugxhksu"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-container brz-css-1luw0hj brz-css-8dnozk"><div class="brz-row__container brz-css-10e8jpe brz-css-1an4vvi" data-brz-custom-id="yppokrsdetlojfjagtucgccaloaggqmqduhq"><div class="brz-row brz-css-1mgbab6 brz-css-187yhb2 brz-css-1gohfum"><div class="brz-columns brz-css-1vf6zvc brz-css-llznuj" data-brz-custom-id="x4IjefNauN3D"><div class="brz-column__items brz-css-m57ec7 brz-css-1z06xgw"><div id="" class="brz-css-1u7d53e brz-css-jvzud0 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-vuz851" data-brz-custom-id="dV6b7WWrpSiP"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_5 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-20 brz-ft-google brz-ff-overpass brz-text-lg-right brz-text-xs-justify brz-tp-lg-subtitle brz-css-nwoKe" data-generated-css="brz-css-kzQEQ" data-uniq-id="v97od"><span class="brz-cp-color2">Σύλλογος</span></p></div></div></div><div id="" class="brz-css-1u7d53e brz-css-slzu2l brz-css-1b6471k brz-wrapper"><div class="brz-spacer brz-css-z59yr brz-css-h5jkn6"></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-18y62i7" data-brz-custom-id="kX2FdVvJQkWS"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-1yvwm5t brz-css-pa6524 brz-css-miyt54" href="/mt-blog" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="uPY4NDsuOH4c"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Blog / Άρθρα</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-127ypgu" data-brz-custom-id="vbdh9x_SizJU"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-pj9c4m brz-css-pa6524 brz-css-1ekb251" href="/mt" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="mDX9DAevYMs_"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Μουσικοθεραπεία</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-y8fpl2" data-brz-custom-id="zPS3Tvt7Ldce"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-1nox22q brz-css-pa6524 brz-css-1lfjiu8" href="/find" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="hRVbnFWj_ISu"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Βρες Μουσικοθεραπευτή</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-1uq4lhf" data-brz-custom-id="ij3BTkfQfQSg"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-7pqe0i brz-css-pa6524 brz-css-5v297a" href="/study" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="fgk1bOc6VXeJ"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Σπουδές Μουσικοθεραπείας</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-1wim2wv" data-brz-custom-id="wh70JUVQU_Eo"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-l98slo brz-css-pa6524 brz-css-1aatpbp" href="mt-info" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="yiM6j1zRzhT8"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Πληροφορίες για Μουσικοθεραπευτές</span></a></div></div></div><div class="brz-columns brz-css-1vf6zvc brz-css-148dahv" data-brz-custom-id="crmdxghqahuloxvbtipxqendznacfsclqses"><div class="brz-column__items brz-css-m57ec7 brz-css-qweajh"><div id="" class="brz-css-1u7d53e brz-css-1y48u96 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-tvsjwg" data-brz-custom-id="jcuwwkscafklpamntqqavghcmwcsxqykgiry"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_5 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-20 brz-ft-google brz-ff-overpass brz-text-xs-justify brz-tp-lg-subtitle brz-css-bnGyO" data-generated-css="brz-css-gyzJs" data-uniq-id="dKg17"><span class="brz-cp-color2">Επικοινωνία</span></p></div></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-19rtcdj" data-brz-custom-id="nwujdtpjmkjqqvtdaspbhmemljbubaciwlkr"><div class="brz-icon__container" data-brz-custom-id="moyzqqiuimsadvitdlgxcoacautxfhtyqzqj"><a class="brz-a" href="https://www.facebook.com/profile.php?id=61572078845247" target="_self" rel="noopener" data-brz-link-type="external"><span class="brz-icon brz-span brz-css-sqbbv8 brz-css-j93mke"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/d710c5ce3ce8671b1521487d79654e3d.svg#nc_icon"></use></svg></span></a></div><div class="brz-icon__container" data-brz-custom-id="kzvpreckspwzepwhzkyecketzinslpohyaii"><a class="brz-a" href="https://www.instagram.com/music_therapy_cy" target="_self" rel="noopener" data-brz-link-type="external"><span class="brz-icon brz-span brz-css-sqbbv8 brz-css-gjsln7"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/207d5e57bd91d810599bb7b29b33c358.svg#nc_icon"></use></svg></span></a></div></div><div id="" class="brz-css-1u7d53e brz-css-slzu2l brz-css-1m3u1b6 brz-wrapper"><div class="brz-spacer brz-css-z59yr brz-css-zwnm8i"></div></div><div id="" class="brz-css-1u7d53e brz-css-qxobrp brz-wrapper"><div class="brz-icon-text brz-css-lc1q61 brz-css-p80si6" data-brz-custom-id="iiqubnsomhhfaerwisytfsfhpzokvodsjedn"><div class="brz-icon__container" data-brz-custom-id="rohhcnktgebfzxzztqymomoefvxgibrtaqfx"><span class="brz-icon brz-span brz-css-sqbbv8 brz-css-1w201we"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/3ab13401b5087e89fe0e4d04b6a9bcd4.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-10df8lu" data-brz-custom-id="ungzlxcfrsvtafskhsmduaaexwewvsphlzrr"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-16 brz-ft-google brz-ff-overpass brz-fs-xs-14 brz-fss-xs-px brz-fw-xs-400 brz-ls-xs-0 brz-lh-xs-1_6 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-text-xs-justify brz-tp-lg-uGuDLCdCXLbq brz-tp-xs-uGuDLCdCXLbq brz-css-qLxIb" data-generated-css="brz-css-cvCqH" data-uniq-id="vhy6J"><a class="link--external brz-cp-color7" href="tel:+35797661501" data-brz-link-type="external">+357 97 661501</a></p></div></div></div></div></div><div id="" class="brz-css-1u7d53e brz-css-7xvgik brz-wrapper"><div class="brz-icon-text brz-css-lc1q61 brz-css-33vbr4" data-brz-custom-id="othzgelrlicbowdprqlkzufpnydfesklmhvm"><div class="brz-icon__container" data-brz-custom-id="kkvgtjapfowujcgtaliixwxyzwepjryupymx"><span class="brz-icon brz-span brz-css-sqbbv8 brz-css-1hdkxre"><svg class="brz-icon-svg align-[initial]"><use href="../assets/svg/3eae82f17391f20cdfc33c0710557915.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-mqvrob" data-brz-custom-id="fdqdynypgohpacgohzzilfaedwwousjhwppv"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-16 brz-ft-google brz-ff-overpass brz-fs-xs-14 brz-fss-xs-px brz-fw-xs-400 brz-ls-xs-0 brz-lh-xs-1_6 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-text-xs-justify brz-tp-lg-uGuDLCdCXLbq brz-tp-xs-uGuDLCdCXLbq brz-css-fMQkB" data-generated-css="brz-css-nKI4g" data-uniq-id="pLGcD"><a class="link--external brz-cp-color7" href="mailto: <EMAIL> " data-brz-link-type="external" target="_blank"><EMAIL> </a></p></div></div></div></div></div><div id="" class="brz-css-1u7d53e brz-css-1yid6jy brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-mpjmry" data-brz-custom-id="aardekiwydvflewsszxkqgtsphtmilutznpl"><div data-brz-translate-text="1"><p class="brz-tp-xs-uGuDLCdCXLbq brz-tp-lg-uGuDLCdCXLbq brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_8 brz-ls-xs-0 brz-fw-xs-400 brz-fss-xs-px brz-fs-xs-14 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-0 brz-lh-lg-2 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-ft-google brz-fs-lg-16 brz-ff-overpass brz-text-xs-left brz-css-y6prp" data-uniq-id="fXT1V" data-generated-css="brz-css-aE7oZ"><span class="brz-cp-color7">Οινόης 7, Στρόβολος 2037 Λευκωσία</span></p></div></div></div></div></div></div></div><div id="" class="brz-css-1u7d53e brz-css-slzu2l brz-css-mbir7l brz-wrapper"><div class="brz-spacer brz-css-z59yr brz-css-1l3j2t3"></div></div><div id="" class="brz-css-1u7d53e brz-css-106cr9e brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-o4546m" data-brz-custom-id="uhvdsajaaizzsumbtpczrowasicltdzgvjlc"><div data-brz-translate-text="1"><p class="brz-tp-lg-uGuDLCdCXLbq brz-text-lg-center brz-ff-overpass brz-ft-google brz-fs-lg-16 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-0 brz-lh-lg-1_6 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-xs-u9Xihr8QXhSs brz-css-gyvoe" data-uniq-id="jJY6l" data-generated-css="brz-css-eBk08"><span class="brz-cp-color7">© 2025 Cyprus Music Therapy Association. </span></p></div></div></div></div></div></section>     </div>          </body></html>